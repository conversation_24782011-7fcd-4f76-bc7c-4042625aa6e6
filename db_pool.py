import pymysql
import logging
import threading
import time
from contextlib import contextmanager
from queue import Queue, Empty
from typing import Optional, Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabasePool:
    """数据库连接池管理器"""
    
    def __init__(self, 
                 host='*************',
                 user='root', 
                 password='windows1',
                 port=3306,
                 database='hospital_appointments',
                 charset='utf8mb4',
                 min_connections=5,
                 max_connections=20,
                 max_idle_time=300,  # 5分钟
                 connection_timeout=10):
        
        self.config = {
            'host': host,
            'user': user,
            'password': password,
            'port': port,
            'database': database,
            'charset': charset,
            'autocommit': True,
            'connect_timeout': connection_timeout,
            'read_timeout': 30,
            'write_timeout': 30,
            'max_allowed_packet': 16*1024*1024
        }
        
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.max_idle_time = max_idle_time
        
        self._pool = Queue(maxsize=max_connections)
        self._active_connections = {}
        self._lock = threading.RLock()
        self._created_connections = 0
        
        # 初始化最小连接数
        self._initialize_pool()
        
        # 启动清理线程
        self._cleanup_thread = threading.Thread(target=self._cleanup_idle_connections, daemon=True)
        self._cleanup_thread.start()
        
        logger.info(f"数据库连接池初始化完成: min={min_connections}, max={max_connections}")
    
    def _create_connection(self):
        """创建新的数据库连接"""
        try:
            connection = pymysql.connect(**self.config)
            connection_id = id(connection)
            logger.debug(f"创建新连接: {connection_id}")
            return connection
        except Exception as e:
            logger.error(f"创建数据库连接失败: {e}")
            raise
    
    def _initialize_pool(self):
        """初始化连接池"""
        with self._lock:
            for _ in range(self.min_connections):
                try:
                    conn = self._create_connection()
                    self._pool.put((conn, time.time()))
                    self._created_connections += 1
                except Exception as e:
                    logger.error(f"初始化连接池失败: {e}")
                    break
    
    def _cleanup_idle_connections(self):
        """清理空闲连接的后台线程"""
        while True:
            try:
                time.sleep(60)  # 每分钟检查一次
                current_time = time.time()
                
                with self._lock:
                    # 临时存储需要保留的连接
                    temp_connections = []
                    
                    # 检查所有连接
                    while not self._pool.empty():
                        try:
                            conn, last_used = self._pool.get_nowait()
                            
                            # 如果连接空闲时间超过阈值且连接数大于最小值
                            if (current_time - last_used > self.max_idle_time and 
                                self._created_connections > self.min_connections):
                                try:
                                    conn.close()
                                    self._created_connections -= 1
                                    logger.debug(f"关闭空闲连接: {id(conn)}")
                                except:
                                    pass
                            else:
                                temp_connections.append((conn, last_used))
                        except Empty:
                            break
                    
                    # 将保留的连接放回池中
                    for conn_info in temp_connections:
                        self._pool.put(conn_info)
                        
            except Exception as e:
                logger.error(f"清理空闲连接时出错: {e}")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        connection = None
        try:
            connection = self._get_connection()
            yield connection
        finally:
            if connection:
                self._return_connection(connection)
    
    def _get_connection(self):
        """从连接池获取连接"""
        with self._lock:
            # 尝试从池中获取连接
            try:
                conn, _ = self._pool.get_nowait()
                # 检查连接是否有效
                if self._is_connection_valid(conn):
                    self._active_connections[id(conn)] = time.time()
                    return conn
                else:
                    # 连接无效，关闭并创建新连接
                    try:
                        conn.close()
                    except:
                        pass
                    self._created_connections -= 1
            except Empty:
                pass
            
            # 如果没有可用连接且未达到最大连接数，创建新连接
            if self._created_connections < self.max_connections:
                conn = self._create_connection()
                self._created_connections += 1
                self._active_connections[id(conn)] = time.time()
                return conn
            else:
                # 等待连接可用
                logger.warning("连接池已满，等待连接释放...")
                conn, _ = self._pool.get(timeout=30)  # 30秒超时
                if self._is_connection_valid(conn):
                    self._active_connections[id(conn)] = time.time()
                    return conn
                else:
                    raise Exception("获取到无效连接")
    
    def _return_connection(self, connection):
        """将连接返回到连接池"""
        with self._lock:
            conn_id = id(connection)
            if conn_id in self._active_connections:
                del self._active_connections[conn_id]
            
            if self._is_connection_valid(connection):
                self._pool.put((connection, time.time()))
            else:
                # 连接无效，关闭并减少计数
                try:
                    connection.close()
                except:
                    pass
                self._created_connections -= 1
                logger.debug(f"关闭无效连接: {conn_id}")
    
    def _is_connection_valid(self, connection):
        """检查连接是否有效"""
        try:
            connection.ping(reconnect=False)
            return True
        except:
            return False
    
    def get_stats(self):
        """获取连接池统计信息"""
        with self._lock:
            return {
                'total_connections': self._created_connections,
                'active_connections': len(self._active_connections),
                'idle_connections': self._pool.qsize(),
                'max_connections': self.max_connections,
                'min_connections': self.min_connections
            }
    
    def close_all(self):
        """关闭所有连接"""
        with self._lock:
            # 关闭活跃连接
            for conn_id in list(self._active_connections.keys()):
                # 注意：这里不能直接关闭活跃连接，因为可能正在使用
                pass
            
            # 关闭池中的连接
            while not self._pool.empty():
                try:
                    conn, _ = self._pool.get_nowait()
                    conn.close()
                except:
                    pass
            
            self._created_connections = 0
            logger.info("所有数据库连接已关闭")

# 全局连接池实例
_db_pool = None
_pool_lock = threading.Lock()

def get_db_pool():
    """获取全局数据库连接池实例"""
    global _db_pool
    if _db_pool is None:
        with _pool_lock:
            if _db_pool is None:
                _db_pool = DatabasePool()
    return _db_pool

def close_db_pool():
    """关闭全局数据库连接池"""
    global _db_pool
    if _db_pool:
        _db_pool.close_all()
        _db_pool = None
