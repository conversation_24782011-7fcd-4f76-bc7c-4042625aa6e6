import json
import time
import logging
import threading
from typing import Any, Optional, Dict, Callable
from functools import wraps

logger = logging.getLogger(__name__)

class MemoryCache:
    """内存缓存管理器（当Redis不可用时的备选方案）"""
    
    def __init__(self, default_timeout=300, max_size=1000):
        self.default_timeout = default_timeout
        self.max_size = max_size
        self._cache = {}
        self._access_times = {}
        self._lock = threading.RLock()
        
        # 启动清理线程
        self._cleanup_thread = threading.Thread(target=self._cleanup_expired, daemon=True)
        self._cleanup_thread.start()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key in self._cache:
                value, expire_time = self._cache[key]
                if expire_time is None or time.time() < expire_time:
                    self._access_times[key] = time.time()
                    return value
                else:
                    # 过期，删除
                    del self._cache[key]
                    if key in self._access_times:
                        del self._access_times[key]
            return None
    
    def set(self, key: str, value: Any, timeout: Optional[int] = None) -> bool:
        """设置缓存值"""
        with self._lock:
            # 检查缓存大小
            if len(self._cache) >= self.max_size and key not in self._cache:
                self._evict_lru()
            
            timeout = timeout or self.default_timeout
            expire_time = time.time() + timeout if timeout > 0 else None
            
            self._cache[key] = (value, expire_time)
            self._access_times[key] = time.time()
            return True
    
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                if key in self._access_times:
                    del self._access_times[key]
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._access_times.clear()
    
    def _evict_lru(self):
        """移除最近最少使用的缓存项"""
        if not self._access_times:
            return
        
        # 找到最久未访问的key
        lru_key = min(self._access_times.keys(), key=lambda k: self._access_times[k])
        if lru_key in self._cache:
            del self._cache[lru_key]
        del self._access_times[lru_key]
    
    def _cleanup_expired(self):
        """清理过期缓存的后台线程"""
        while True:
            try:
                time.sleep(60)  # 每分钟清理一次
                current_time = time.time()
                
                with self._lock:
                    expired_keys = []
                    for key, (value, expire_time) in self._cache.items():
                        if expire_time is not None and current_time >= expire_time:
                            expired_keys.append(key)
                    
                    for key in expired_keys:
                        del self._cache[key]
                        if key in self._access_times:
                            del self._access_times[key]
                    
                    if expired_keys:
                        logger.debug(f"清理了 {len(expired_keys)} 个过期缓存项")
                        
            except Exception as e:
                logger.error(f"清理过期缓存时出错: {e}")
    
    def get_stats(self):
        """获取缓存统计信息"""
        with self._lock:
            return {
                'total_keys': len(self._cache),
                'max_size': self.max_size,
                'default_timeout': self.default_timeout
            }

class CacheManager:
    """缓存管理器，支持Redis和内存缓存"""
    
    def __init__(self):
        self.redis_client = None
        self.memory_cache = MemoryCache()
        self._init_redis()
    
    def _init_redis(self):
        """初始化Redis连接"""
        try:
            import redis
            self.redis_client = redis.Redis(
                host='localhost',
                port=6379,
                db=0,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            # 测试连接
            self.redis_client.ping()
            logger.info("Redis缓存已启用")
        except Exception as e:
            logger.warning(f"Redis连接失败，使用内存缓存: {e}")
            self.redis_client = None
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            if self.redis_client:
                value = self.redis_client.get(key)
                if value:
                    return json.loads(value)
            else:
                return self.memory_cache.get(key)
        except Exception as e:
            logger.error(f"获取缓存失败 {key}: {e}")
        return None
    
    def set(self, key: str, value: Any, timeout: int = 300) -> bool:
        """设置缓存值"""
        try:
            if self.redis_client:
                return self.redis_client.setex(key, timeout, json.dumps(value, ensure_ascii=False))
            else:
                return self.memory_cache.set(key, value, timeout)
        except Exception as e:
            logger.error(f"设置缓存失败 {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        try:
            if self.redis_client:
                return bool(self.redis_client.delete(key))
            else:
                return self.memory_cache.delete(key)
        except Exception as e:
            logger.error(f"删除缓存失败 {key}: {e}")
            return False
    
    def clear_pattern(self, pattern: str):
        """清除匹配模式的缓存"""
        try:
            if self.redis_client:
                keys = self.redis_client.keys(pattern)
                if keys:
                    self.redis_client.delete(*keys)
            else:
                # 内存缓存不支持模式匹配，清空所有
                self.memory_cache.clear()
        except Exception as e:
            logger.error(f"清除缓存模式失败 {pattern}: {e}")
    
    def get_stats(self):
        """获取缓存统计信息"""
        if self.redis_client:
            try:
                info = self.redis_client.info()
                return {
                    'type': 'redis',
                    'used_memory': info.get('used_memory_human', 'N/A'),
                    'connected_clients': info.get('connected_clients', 0),
                    'total_commands_processed': info.get('total_commands_processed', 0)
                }
            except:
                pass
        
        return {
            'type': 'memory',
            **self.memory_cache.get_stats()
        }

# 全局缓存管理器实例
_cache_manager = None
_cache_lock = threading.Lock()

def get_cache_manager():
    """获取全局缓存管理器实例"""
    global _cache_manager
    if _cache_manager is None:
        with _cache_lock:
            if _cache_manager is None:
                _cache_manager = CacheManager()
    return _cache_manager

def cached(timeout: int = 300, key_prefix: str = ""):
    """缓存装饰器"""
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存key
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 尝试从缓存获取
            cache = get_cache_manager()
            result = cache.get(cache_key)
            
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout)
            return result
        
        return wrapper
    return decorator
