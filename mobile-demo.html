<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>移动端预约列表演示</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-light: #818cf8;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --background-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            --border-radius: 16px;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--background-gradient);
            min-height: 100vh;
            padding: 20px 0;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .content-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            background: #f8fafc;
        }

        .card-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .status-select {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            width: 100%;
        }

        /* 移动端预约列表样式 */
        .mobile-appointment-list {
            max-height: 70vh;
            overflow-y: auto;
            overflow-x: hidden;
            padding-right: 4px;
            margin-right: -4px;
        }

        .mobile-appointment-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .mobile-appointment-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .mobile-appointment-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
        }

        .mobile-patient-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }

        .mobile-appointment-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .mobile-info-item {
            display: flex;
            flex-direction: column;
        }

        .mobile-info-label {
            font-size: 0.75rem;
            color: #64748b;
            font-weight: 500;
            margin-bottom: 0.25rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .mobile-info-value {
            font-size: 0.9rem;
            color: #334155;
            font-weight: 500;
        }

        .mobile-phone-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
        }

        .mobile-phone-link:hover {
            text-decoration: underline;
        }

        .mobile-status-container {
            margin-top: 1rem;
        }

        .mobile-status-label {
            font-size: 0.75rem;
            color: #64748b;
            font-weight: 500;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        /* 桌面端表格样式 */
        .table-container {
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }

        .table {
            margin: 0;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: none;
            padding: 1rem;
            font-weight: 600;
            color: #475569;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .table tbody td {
            padding: 1rem;
            border-top: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: #f8fafc;
        }

        /* 移动端滚动条样式 */
        .mobile-appointment-list::-webkit-scrollbar {
            width: 6px;
        }

        .mobile-appointment-list::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .mobile-appointment-list::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
            transition: background 0.2s ease;
        }

        .mobile-appointment-list::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Firefox 滚动条样式 */
        .mobile-appointment-list {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }

        /* 滚动指示器 */
        .mobile-scroll-indicator {
            position: sticky;
            top: 0;
            background: linear-gradient(to bottom, rgba(248, 250, 252, 0.9), transparent);
            height: 20px;
            margin-bottom: -20px;
            z-index: 1;
            pointer-events: none;
        }

        .mobile-scroll-indicator::after {
            content: '';
            position: absolute;
            top: 5px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: #cbd5e1;
            border-radius: 2px;
            opacity: 0.6;
        }

        /* 响应式控制 */
        @media (min-width: 769px) {
            .mobile-appointment-list {
                display: none !important;
            }
        }

        @media (max-width: 768px) {
            .desktop-table-container {
                display: none !important;
            }
            
            .main-container {
                padding: 0 15px;
            }
            
            .card-body {
                padding: 1rem;
            }
            
            .status-select {
                font-size: 16px;
                padding: 12px 14px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 预约列表 -->
        <div class="content-card">
            <div class="card-header">
                <h3><i class="bi bi-list-check"></i> 预约列表</h3>
            </div>
            <div class="card-body">
                <!-- 桌面端表格视图 -->
                <div class="desktop-table-container">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>编号</th>
                                    <th>患者</th>
                                    <th>联系方式</th>
                                    <th>日期</th>
                                    <th>时间段</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-muted">#001</td>
                                    <td class="fw-semibold">张三</td>
                                    <td><a href="tel:13800138000" class="text-decoration-none">13800138000</a></td>
                                    <td>2024-01-15</td>
                                    <td>上午08:00-09:00</td>
                                    <td>
                                        <select class="status-select">
                                            <option value="未到" selected>未到</option>
                                            <option value="已就诊">已就诊</option>
                                            <option value="已取消">已取消</option>
                                        </select>
                                    </td>
                                    <td>
                                        <button class="btn btn-outline-danger btn-sm">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">#002</td>
                                    <td class="fw-semibold">李四</td>
                                    <td><a href="tel:13900139000" class="text-decoration-none">13900139000</a></td>
                                    <td>2024-01-15</td>
                                    <td>上午09:00-10:00</td>
                                    <td>
                                        <select class="status-select">
                                            <option value="未到">未到</option>
                                            <option value="已就诊" selected>已就诊</option>
                                            <option value="已取消">已取消</option>
                                        </select>
                                    </td>
                                    <td>
                                        <button class="btn btn-outline-danger btn-sm">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 移动端卡片视图 -->
                <div class="mobile-appointment-list d-block d-md-none">
                    <div class="mobile-scroll-indicator"></div>
                    <div class="mobile-appointment-card">
                        <div class="mobile-appointment-header">
                            <h4 class="mobile-patient-name">张三</h4>
                            <span class="text-muted small">#001</span>
                        </div>
                        
                        <div class="mobile-appointment-info">
                            <div class="mobile-info-item">
                                <div class="mobile-info-label">联系方式</div>
                                <div class="mobile-info-value">
                                    <a href="tel:13800138000" class="mobile-phone-link">
                                        <i class="bi bi-telephone"></i> 13800138000
                                    </a>
                                </div>
                            </div>
                            
                            <div class="mobile-info-item">
                                <div class="mobile-info-label">预约日期</div>
                                <div class="mobile-info-value">
                                    <i class="bi bi-calendar"></i> 2024-01-15
                                </div>
                            </div>
                        </div>
                        
                        <div class="mobile-status-container">
                            <div class="mobile-status-label">状态</div>
                            <select class="status-select">
                                <option value="未到" selected>未到</option>
                                <option value="已就诊">已就诊</option>
                                <option value="已取消">已取消</option>
                            </select>
                        </div>
                    </div>

                    <div class="mobile-appointment-card">
                        <div class="mobile-appointment-header">
                            <h4 class="mobile-patient-name">李四</h4>
                            <span class="text-muted small">#002</span>
                        </div>
                        
                        <div class="mobile-appointment-info">
                            <div class="mobile-info-item">
                                <div class="mobile-info-label">联系方式</div>
                                <div class="mobile-info-value">
                                    <a href="tel:13900139000" class="mobile-phone-link">
                                        <i class="bi bi-telephone"></i> 13900139000
                                    </a>
                                </div>
                            </div>
                            
                            <div class="mobile-info-item">
                                <div class="mobile-info-label">预约日期</div>
                                <div class="mobile-info-value">
                                    <i class="bi bi-calendar"></i> 2024-01-15
                                </div>
                            </div>
                        </div>
                        
                        <div class="mobile-status-container">
                            <div class="mobile-status-label">状态</div>
                            <select class="status-select">
                                <option value="未到">未到</option>
                                <option value="已就诊" selected>已就诊</option>
                                <option value="已取消">已取消</option>
                            </select>
                        </div>
                    </div>

                    <div class="mobile-appointment-card">
                        <div class="mobile-appointment-header">
                            <h4 class="mobile-patient-name">王五</h4>
                            <span class="text-muted small">#003</span>
                        </div>

                        <div class="mobile-appointment-info">
                            <div class="mobile-info-item">
                                <div class="mobile-info-label">联系方式</div>
                                <div class="mobile-info-value">
                                    <a href="tel:13700137000" class="mobile-phone-link">
                                        <i class="bi bi-telephone"></i> 13700137000
                                    </a>
                                </div>
                            </div>

                            <div class="mobile-info-item">
                                <div class="mobile-info-label">预约日期</div>
                                <div class="mobile-info-value">
                                    <i class="bi bi-calendar"></i> 2024-01-16
                                </div>
                            </div>
                        </div>

                        <div class="mobile-status-container">
                            <div class="mobile-status-label">状态</div>
                            <select class="status-select">
                                <option value="未到">未到</option>
                                <option value="已就诊">已就诊</option>
                                <option value="已取消" selected>已取消</option>
                            </select>
                        </div>
                    </div>

                    <div class="mobile-appointment-card">
                        <div class="mobile-appointment-header">
                            <h4 class="mobile-patient-name">赵六</h4>
                            <span class="text-muted small">#004</span>
                        </div>

                        <div class="mobile-appointment-info">
                            <div class="mobile-info-item">
                                <div class="mobile-info-label">联系方式</div>
                                <div class="mobile-info-value">
                                    <a href="tel:13600136000" class="mobile-phone-link">
                                        <i class="bi bi-telephone"></i> 13600136000
                                    </a>
                                </div>
                            </div>

                            <div class="mobile-info-item">
                                <div class="mobile-info-label">预约日期</div>
                                <div class="mobile-info-value">
                                    <i class="bi bi-calendar"></i> 2024-01-16
                                </div>
                            </div>
                        </div>

                        <div class="mobile-status-container">
                            <div class="mobile-status-label">状态</div>
                            <select class="status-select">
                                <option value="未到" selected>未到</option>
                                <option value="已就诊">已就诊</option>
                                <option value="已取消">已取消</option>
                            </select>
                        </div>
                    </div>

                    <div class="mobile-appointment-card">
                        <div class="mobile-appointment-header">
                            <h4 class="mobile-patient-name">孙七</h4>
                            <span class="text-muted small">#005</span>
                        </div>

                        <div class="mobile-appointment-info">
                            <div class="mobile-info-item">
                                <div class="mobile-info-label">联系方式</div>
                                <div class="mobile-info-value">
                                    <a href="tel:13500135000" class="mobile-phone-link">
                                        <i class="bi bi-telephone"></i> 13500135000
                                    </a>
                                </div>
                            </div>

                            <div class="mobile-info-item">
                                <div class="mobile-info-label">预约日期</div>
                                <div class="mobile-info-value">
                                    <i class="bi bi-calendar"></i> 2024-01-17
                                </div>
                            </div>
                        </div>

                        <div class="mobile-status-container">
                            <div class="mobile-status-label">状态</div>
                            <select class="status-select">
                                <option value="未到">未到</option>
                                <option value="已就诊" selected>已就诊</option>
                                <option value="已取消">已取消</option>
                            </select>
                        </div>
                    </div>

                    <div class="mobile-appointment-card">
                        <div class="mobile-appointment-header">
                            <h4 class="mobile-patient-name">周八</h4>
                            <span class="text-muted small">#006</span>
                        </div>

                        <div class="mobile-appointment-info">
                            <div class="mobile-info-item">
                                <div class="mobile-info-label">联系方式</div>
                                <div class="mobile-info-value">
                                    <a href="tel:13400134000" class="mobile-phone-link">
                                        <i class="bi bi-telephone"></i> 13400134000
                                    </a>
                                </div>
                            </div>

                            <div class="mobile-info-item">
                                <div class="mobile-info-label">预约日期</div>
                                <div class="mobile-info-value">
                                    <i class="bi bi-calendar"></i> 2024-01-17
                                </div>
                            </div>
                        </div>

                        <div class="mobile-status-container">
                            <div class="mobile-status-label">状态</div>
                            <select class="status-select">
                                <option value="未到" selected>未到</option>
                                <option value="已就诊">已就诊</option>
                                <option value="已取消">已取消</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
