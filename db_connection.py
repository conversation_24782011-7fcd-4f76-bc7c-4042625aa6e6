import pymysql
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)  # 生产环境改为INFO
logger = logging.getLogger(__name__)

class Database:
    def __init__(self):
        try:
            # 优化的数据库连接配置
            self.connection = pymysql.connect(
                host='*************',
                user='root',
                password='windows1',
                port=3306,
                charset='utf8mb4',
                autocommit=True,
                # 性能优化配置
                connect_timeout=10,
                read_timeout=30,
                write_timeout=30,
                max_allowed_packet=16*1024*1024
            )
            # 使用默认的元组游标，简单可靠
            self.cursor = self.connection.cursor()
            
            # 创建数据库（如果不存在）
            self.cursor.execute("""
                CREATE DATABASE IF NOT EXISTS hospital_appointments
                DEFAULT CHARACTER SET utf8mb4 
                DEFAULT COLLATE utf8mb4_general_ci
            """)
            logger.debug("数据库创建成功或已存在")

            # 使用数据库
            self.cursor.execute("USE hospital_appointments")

            # 删除旧表
            #self.cursor.execute("DROP TABLE IF EXISTS pelvic_rehab")
            #logger.debug("删除旧表成功")

            # 创建盆底康复预约表（新结构）
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS pelvic_rehab (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    daily_number INT NOT NULL COMMENT '每日预约编号',
                    name VARCHAR(50) NOT NULL COMMENT '姓名',
                    phone VARCHAR(11) NOT NULL COMMENT '手机号',
                    appointment_date DATE NOT NULL COMMENT '预约日期',
                    time_slot VARCHAR(20) NOT NULL COMMENT '时间段',
                    created_at DATE NOT NULL DEFAULT (CURRENT_DATE) COMMENT '创建日期',
                    INDEX `idx_phone_date_time` (`phone`, `appointment_date`, `time_slot`) COMMENT '查询索引'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='盆底康复预约表'
            """)
            logger.debug("盆底康复预约表创建成功")

            # 创建中医特色治疗预约表
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS tcm_treatment (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    daily_number INT NOT NULL COMMENT '每日预约编号',
                    name VARCHAR(50) NOT NULL COMMENT '姓名',
                    phone VARCHAR(11) NOT NULL COMMENT '手机号',
                    appointment_date DATE NOT NULL COMMENT '预约日期',
                    time_slot VARCHAR(20) NOT NULL COMMENT '时间段',
                    status VARCHAR(10) DEFAULT '未到' COMMENT '就诊状态',
                    created_at DATE NOT NULL DEFAULT (CURRENT_DATE) COMMENT '创建日期',
                    INDEX `idx_phone_date_time` (`phone`, `appointment_date`, `time_slot`) COMMENT '查询索引'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='中医特色治疗预约表'
            """)
            logger.debug("中医特色治疗预约表创建成功")

            # 创建盆底康复时间段管理表
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS pelvic_time_slots (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    label VARCHAR(50) NOT NULL COMMENT '时间段标签',
                    max_count INT NOT NULL DEFAULT 5 COMMENT '最大预约人数',
                    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='盆底康复时间段管理表'
            """)
            logger.debug("盆底康复时间段管理表创建成功")

            # 创建中医特色治疗时间段管理表
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS tcm_time_slots (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    label VARCHAR(50) NOT NULL COMMENT '时间段标签',
                    max_count INT NOT NULL DEFAULT 3 COMMENT '最大预约人数',
                    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='中医特色治疗时间段管理表'
            """)
            logger.debug("中医特色治疗时间段管理表创建成功")

            # 初始化盆底康复默认时间段数据（如果表为空）
            self.cursor.execute("SELECT COUNT(*) FROM pelvic_time_slots")
            count = self.cursor.fetchone()[0]
            if count == 0:
                pelvic_default_slots = [
                    ('上午08:00-09:00', 5),
                    ('上午09:00-10:00', 5),
                    ('上午10:00-11:00', 5),
                    ('上午11:00-12:00', 5),
                    ('下午14:30-15:30', 5),
                    ('下午15:30-16:30', 5),
                    ('下午16:30-17:30', 5)
                ]
                for label, max_count in pelvic_default_slots:
                    self.cursor.execute(
                        "INSERT INTO pelvic_time_slots (label, max_count) VALUES (%s, %s)",
                        (label, max_count)
                    )
                logger.debug("盆底康复默认时间段数据初始化完成")

            # 初始化中医特色治疗默认时间段数据（如果表为空）
            self.cursor.execute("SELECT COUNT(*) FROM tcm_time_slots")
            count = self.cursor.fetchone()[0]
            if count == 0:
                tcm_default_slots = [
                    ('上午08:00-09:00', 3),
                    ('上午09:00-10:00', 3),
                    ('上午10:00-11:00', 3),
                    ('上午11:00-12:00', 3),
                    ('下午14:30-15:30', 3),
                    ('下午15:30-16:30', 3),
                    ('下午16:30-17:30', 3)
                ]
                for label, max_count in tcm_default_slots:
                    self.cursor.execute(
                        "INSERT INTO tcm_time_slots (label, max_count) VALUES (%s, %s)",
                        (label, max_count)
                    )
                logger.debug("中医特色治疗默认时间段数据初始化完成")



            # 重新连接（指定数据库）
            self.close()
            self.connection = pymysql.connect(
                host='*************',
                user='root',
                password='windows1',
                port=3306,
                database='hospital_appointments',
                charset='utf8mb4',
                autocommit=True
            )
            self.cursor = self.connection.cursor()
            logger.debug("数据库连接成功")

        except pymysql.MySQLError as err:
            logger.error(f"数据库连接或初始化错误: {err}")
            raise

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def add_rehab_appointment(self, name, phone, appointment_date, time_slot):
        try:
            # 获取当天的最大编号
            get_max_number_query = """
                SELECT COALESCE(MAX(daily_number), 0)
                FROM pelvic_rehab
                WHERE DATE(created_at) = CURRENT_DATE
            """
            self.cursor.execute(get_max_number_query)
            max_number = self.cursor.fetchone()[0]
            new_number = max_number + 1

            # 检查是否存在当天创建的预约
            check_query = """
                SELECT id, daily_number FROM pelvic_rehab 
                WHERE phone = %s 
                AND DATE(created_at) = CURRENT_DATE
            """
            self.cursor.execute(check_query, (phone,))
            existing = self.cursor.fetchone()
            
            if existing:
                # 如果存在当天创建的预约，则更新但保留原编号
                update_query = """
                    UPDATE pelvic_rehab 
                    SET name = %s, appointment_date = %s, time_slot = %s
                    WHERE id = %s
                """
                self.cursor.execute(update_query, (name, appointment_date, time_slot, existing[0]))
                self.connection.commit()
                logger.debug(f"更新今日预约: {phone}, 预约编号: {existing[1]}")
                return True, "更新", existing[1]
            else:
                # 不存在当天创建的预约，创建新预约并分配新编号
                insert_query = """
                    INSERT INTO pelvic_rehab 
                    (daily_number, name, phone, appointment_date, time_slot)
                    VALUES (%s, %s, %s, %s, %s)
                """
                self.cursor.execute(insert_query, (new_number, name, phone, appointment_date, time_slot))
                self.connection.commit()
                logger.debug(f"新预约添加成功: {phone}, 预约编号: {new_number}")
                return True, "新增", new_number
                
        except pymysql.MySQLError as err:
            logger.error(f"数据库操作错误: {err}")
            self.connection.rollback()
            return False, None, None

    def add_tcm_appointment(self, name, phone, appointment_date, time_slot):
        try:
            # 获取当天的最大编号
            get_max_number_query = """
                SELECT COALESCE(MAX(daily_number), 0)
                FROM tcm_treatment
                WHERE DATE(created_at) = CURRENT_DATE
            """
            self.cursor.execute(get_max_number_query)
            max_number = self.cursor.fetchone()[0]
            new_number = max_number + 1

            # 检查是否存在当天创建的预约
            check_query = """
                SELECT id, daily_number FROM tcm_treatment
                WHERE phone = %s
                AND DATE(created_at) = CURRENT_DATE
            """
            self.cursor.execute(check_query, (phone,))
            existing = self.cursor.fetchone()

            if existing:
                # 如果存在当天创建的预约，则更新但保留原编号
                update_query = """
                    UPDATE tcm_treatment
                    SET name = %s, appointment_date = %s, time_slot = %s
                    WHERE id = %s
                """
                self.cursor.execute(update_query, (name, appointment_date, time_slot, existing[0]))
                self.connection.commit()
                logger.debug(f"更新今日中医预约: {phone}, 预约编号: {existing[1]}")
                return True, "更新", existing[1]
            else:
                # 不存在当天创建的预约，创建新预约并分配新编号
                insert_query = """
                    INSERT INTO tcm_treatment
                    (daily_number, name, phone, appointment_date, time_slot)
                    VALUES (%s, %s, %s, %s, %s)
                """
                self.cursor.execute(insert_query, (new_number, name, phone, appointment_date, time_slot))
                self.connection.commit()
                logger.debug(f"新中医预约添加成功: {phone}, 预约编号: {new_number}")
                return True, "新增", new_number

        except pymysql.MySQLError as err:
            logger.error(f"中医预约数据库操作错误: {err}")
            self.connection.rollback()
            return False, None, None

    def get_appointment_count(self, appointment_type, appointment_date, time_slot):
        try:
            # 支持盆底康复预约和中医特色治疗预约
            if appointment_type == 'pelvic_rehab':
                table = 'pelvic_rehab'
            elif appointment_type == 'tcm_treatment':
                table = 'tcm_treatment'
            else:
                return 0
            query = f"""
                SELECT COUNT(*)
                FROM {table}
                WHERE appointment_date = %s AND time_slot = %s
            """
            self.cursor.execute(query, (appointment_date, time_slot))
            result = self.cursor.fetchone()
            return result[0] if result else 0
        except pymysql.MySQLError as err:
            logger.error(f"查询预约数量错误: {err}")
            return 0

    def close(self):
        try:
            if hasattr(self, 'cursor') and self.cursor:
                self.cursor.close()
            if hasattr(self, 'connection') and self.connection:
                self.connection.close()
            logger.debug("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭连接错误: {e}")

    def get_rehab_appointments(self, phone):
        try:
            query = """
                SELECT name, phone, appointment_date, time_slot, daily_number, created_at
                FROM pelvic_rehab
                WHERE phone = %s
                ORDER BY created_at DESC, appointment_date ASC
                LIMIT 5
            """
            self.cursor.execute(query, (phone,))
            results = self.cursor.fetchall()
            
            appointments = []
            for row in results:
                appointments.append({
                    'name': row[0],
                    'phone': row[1],
                    'appointment_date': row[2].strftime('%Y年%m月%d日'),
                    'time_slot': row[3],
                    'daily_number': row[4],
                    'created_at': row[5].strftime('%Y年%m月%d日')
                })
            return appointments
        except pymysql.MySQLError as err:
            logger.error(f"查询预约记录错误: {err}")
            return None

    def get_tcm_appointments(self, phone):
        try:
            query = """
                SELECT name, phone, appointment_date, time_slot, daily_number, created_at, status
                FROM tcm_treatment
                WHERE phone = %s
                ORDER BY created_at DESC, appointment_date ASC
                LIMIT 5
            """
            self.cursor.execute(query, (phone,))
            results = self.cursor.fetchall()

            appointments = []
            for row in results:
                appointments.append({
                    'name': row[0],
                    'phone': row[1],
                    'appointment_date': row[2].strftime('%Y年%m月%d日'),
                    'time_slot': row[3],
                    'daily_number': row[4],
                    'created_at': row[5].strftime('%Y年%m月%d日'),
                    'status': row[6]
                })
            return appointments
        except pymysql.MySQLError as err:
            logger.error(f"查询中医预约记录错误: {err}")
            return None

    def get_all_rehab_appointments(self):
        try:
            query = """
                SELECT id, name, phone, appointment_date, time_slot, \
                       IFNULL(status, '未到') as status, daily_number, created_at
                FROM pelvic_rehab
                ORDER BY appointment_date DESC, time_slot ASC, id DESC
            """
            self.cursor.execute(query)
            results = self.cursor.fetchall()
            appointments = []
            for row in results:
                appointments.append({
                    'id': row[0],
                    'name': row[1],
                    'phone': row[2],
                    'date': row[3].strftime('%Y-%m-%d'),
                    'time_slot': row[4],
                    'status': row[5],
                    'daily_number': row[6],
                    'created_at': row[7].strftime('%Y-%m-%d')
                })
            return appointments
        except Exception as e:
            logger.error(f'获取所有盆底康复预约失败: {e}')
            return []

    def get_all_tcm_appointments(self):
        try:
            query = """
                SELECT id, name, phone, appointment_date, time_slot,
                       IFNULL(status, '未到') as status, daily_number, created_at
                FROM tcm_treatment
                ORDER BY appointment_date DESC, time_slot ASC, id DESC
            """
            self.cursor.execute(query)
            results = self.cursor.fetchall()
            appointments = []
            for row in results:
                appointments.append({
                    'id': row[0],
                    'name': row[1],
                    'phone': row[2],
                    'date': row[3].strftime('%Y-%m-%d'),
                    'time_slot': row[4],
                    'status': row[5],
                    'daily_number': row[6],
                    'created_at': row[7].strftime('%Y-%m-%d')
                })
            return appointments
        except Exception as e:
            logger.error(f'获取所有中医特色治疗预约失败: {e}')
            return []

    def update_appointment_status(self, appointment_id, new_status):
        try:
            # 如果没有status字段，请先在表中加一列: ALTER TABLE pelvic_rehab ADD COLUMN status VARCHAR(10) DEFAULT '未到';
            query = "UPDATE pelvic_rehab SET status = %s WHERE id = %s"
            self.cursor.execute(query, (new_status, appointment_id))
            self.connection.commit()
        except Exception as e:
            logger.error(f'更新预约状态失败: {e}')

    def update_tcm_appointment_status(self, appointment_id, new_status):
        try:
            query = "UPDATE tcm_treatment SET status = %s WHERE id = %s"
            self.cursor.execute(query, (new_status, appointment_id))
            self.connection.commit()
        except Exception as e:
            logger.error(f'更新中医预约状态失败: {e}')

    def search_rehab_patients(self, keyword):
        """搜索盆底康复患者信息"""
        try:
            keyword = f"%{keyword}%"
            query = """
                SELECT id, name, phone, appointment_date, time_slot, 
                       IFNULL(status, '未到') as status, daily_number, created_at
                FROM pelvic_rehab 
                WHERE name LIKE %s OR phone LIKE %s
                ORDER BY appointment_date DESC, time_slot ASC, id DESC
            """
            self.cursor.execute(query, (keyword, keyword))
            results = self.cursor.fetchall()
            appointments = []
            for row in results:
                appointments.append({
                    'id': row[0],
                    'name': row[1],
                    'phone': row[2],
                    'date': row[3].strftime('%Y-%m-%d'),
                    'time_slot': row[4],
                    'status': row[5],
                    'daily_number': row[6],
                    'created_at': row[7].strftime('%Y-%m-%d')
                })
            return appointments
        except Exception as e:
            logger.error(f'搜索盆底康复患者失败: {e}')
            return []

    def search_tcm_patients(self, keyword):
        """搜索中医特色治疗患者信息"""
        try:
            keyword = f"%{keyword}%"
            query = """
                SELECT id, name, phone, appointment_date, time_slot,
                       IFNULL(status, '未到') as status, daily_number, created_at
                FROM tcm_treatment 
                WHERE name LIKE %s OR phone LIKE %s
                ORDER BY appointment_date DESC, time_slot ASC, id DESC
            """
            self.cursor.execute(query, (keyword, keyword))
            results = self.cursor.fetchall()
            appointments = []
            for row in results:
                appointments.append({
                    'id': row[0],
                    'name': row[1],
                    'phone': row[2],
                    'date': row[3].strftime('%Y-%m-%d'),
                    'time_slot': row[4],
                    'status': row[5],
                    'daily_number': row[6],
                    'created_at': row[7].strftime('%Y-%m-%d')
                })
            return appointments
        except Exception as e:
            logger.error(f'搜索中医特色治疗患者失败: {e}')
            return []

    def get_patient_detailed_info(self, keyword, treatment_type='rehab'):
        """获取患者详细信息，包括预约历史和统计"""
        try:
            keyword = f"%{keyword}%"
            if treatment_type == 'rehab':
                table_name = 'pelvic_rehab'
            else:
                table_name = 'tcm_treatment'
            
            # 获取预约记录
            query = f"""
                SELECT id, name, phone, appointment_date, time_slot,
                       IFNULL(status, '未到') as status, daily_number, created_at
                FROM {table_name} 
                WHERE name LIKE %s OR phone LIKE %s
                ORDER BY appointment_date DESC, time_slot ASC
            """
            self.cursor.execute(query, (keyword, keyword))
            appointments = self.cursor.fetchall()
            
            # 获取统计信息
            stats_query = f"""
                SELECT name, phone, COUNT(*) as total_count,
                       SUM(CASE WHEN status = '已就诊' THEN 1 ELSE 0 END) as completed_count,
                       SUM(CASE WHEN status = '未到' THEN 1 ELSE 0 END) as pending_count,
                       SUM(CASE WHEN status = '已取消' THEN 1 ELSE 0 END) as cancelled_count,
                       MIN(appointment_date) as first_appointment,
                       MAX(appointment_date) as last_appointment
                FROM {table_name} 
                WHERE name LIKE %s OR phone LIKE %s
                GROUP BY name, phone
            """
            self.cursor.execute(stats_query, (keyword, keyword))
            stats = self.cursor.fetchall()
            
            # 格式化结果
            result = {
                'appointments': [],
                'stats': []
            }
            
            # 格式化预约记录
            for row in appointments:
                result['appointments'].append({
                    'id': row[0],
                    'name': row[1],
                    'phone': row[2],
                    'date': row[3].strftime('%Y-%m-%d'),
                    'time_slot': row[4],
                    'status': row[5],
                    'daily_number': row[6],
                    'created_at': row[7].strftime('%Y-%m-%d')
                })
            
            # 格式化统计信息
            for row in stats:
                result['stats'].append({
                    'name': row[0],
                    'phone': row[1],
                    'total_count': row[2],
                    'completed_count': row[3],
                    'pending_count': row[4],
                    'cancelled_count': row[5],
                    'first_appointment': row[6].strftime('%Y-%m-%d') if row[6] else None,
                    'last_appointment': row[7].strftime('%Y-%m-%d') if row[7] else None
                })
            
            return result
        except Exception as e:
            logger.error(f'获取患者详细信息失败: {e}')
            return {'appointments': [], 'stats': []}

    def delete_appointment(self, appointment_id):
        try:
            query = "DELETE FROM pelvic_rehab WHERE id = %s"
            self.cursor.execute(query, (appointment_id,))
            self.connection.commit()
        except Exception as e:
            logger.error(f'删除预约失败: {e}')

    def delete_tcm_appointment(self, appointment_id):
        try:
            query = "DELETE FROM tcm_treatment WHERE id = %s"
            self.cursor.execute(query, (appointment_id,))
            self.connection.commit()
        except Exception as e:
            logger.error(f'删除中医预约失败: {e}')

    def get_rehab_statistics(self):
        try:
            query = """
                SELECT name, phone, COUNT(*) as count
                FROM pelvic_rehab
                WHERE status = '已就诊'
                GROUP BY name, phone
                ORDER BY count DESC
            """
            self.cursor.execute(query)
            results = self.cursor.fetchall()
            stats = []
            for row in results:
                stats.append({
                    'name': row[0],
                    'phone': row[1],
                    'count': row[2]
                })
            return stats
        except Exception as e:
            logger.error(f'统计预约次数失败: {e}')
            return []

    def get_tcm_statistics(self):
        try:
            query = """
                SELECT name, phone, COUNT(*) as count
                FROM tcm_treatment
                WHERE status = '已就诊'
                GROUP BY name, phone
                ORDER BY count DESC
            """
            self.cursor.execute(query)
            results = self.cursor.fetchall()
            stats = []
            for row in results:
                stats.append({
                    'name': row[0],
                    'phone': row[1],
                    'count': row[2]
                })
            return stats
        except Exception as e:
            logger.error(f'统计中医预约次数失败: {e}')
            return []

    def get_daily_tcm_patients(self, query_date):
        try:
            query = """
                SELECT id, name, phone, appointment_date, time_slot, IFNULL(status, '未到') as status
                FROM tcm_treatment
                WHERE appointment_date = %s
                ORDER BY time_slot ASC, id ASC
            """
            self.cursor.execute(query, (query_date,))
            results = self.cursor.fetchall()
            return [
                {
                    'id': row[0],
                    'name': row[1],
                    'phone': row[2],
                    'date': row[3].strftime('%Y-%m-%d'),
                    'time_slot': row[4],
                    'status': row[5]
                }
                for row in results
            ]
        except Exception as e:
            logger.error(f'获取每日中医预约病人失败: {e}')
            return []

    def get_pelvic_time_slots(self):
        try:
            self.cursor.execute("SELECT id, label, enabled, max_count FROM pelvic_time_slots ORDER BY id ASC")
            results = self.cursor.fetchall()

            # 将元组转换为字典格式，保持API一致性
            time_slots = []
            for row in results:
                time_slots.append({
                    'id': row[0],
                    'label': row[1],
                    'enabled': bool(row[2]),  # 确保布尔类型
                    'max_count': row[3]
                })

            return time_slots
        except Exception as e:
            logger.error(f'获取盆底康复时间段失败: {e}')
            return []

    def get_tcm_time_slots(self):
        try:
            self.cursor.execute("SELECT id, label, enabled, max_count FROM tcm_time_slots ORDER BY id ASC")
            results = self.cursor.fetchall()

            # 将元组转换为字典格式，保持API一致性
            time_slots = []
            for row in results:
                time_slots.append({
                    'id': row[0],
                    'label': row[1],
                    'enabled': bool(row[2]),  # 确保布尔类型
                    'max_count': row[3]
                })

            return time_slots
        except Exception as e:
            logger.error(f'获取中医特色治疗时间段失败: {e}')
            return []

    # 保持向后兼容性的方法
    def get_all_time_slots(self):
        # 默认返回盆底康复时间段，保持向后兼容
        return self.get_pelvic_time_slots()

    def add_pelvic_time_slot(self, label, max_count):
        try:
            self.cursor.execute("INSERT INTO pelvic_time_slots (label, max_count) VALUES (%s, %s)", (label, max_count))
            self.connection.commit()
        except Exception as e:
            logger.error(f'添加盆底康复时间段失败: {e}')

    def add_tcm_time_slot(self, label, max_count):
        try:
            self.cursor.execute("INSERT INTO tcm_time_slots (label, max_count) VALUES (%s, %s)", (label, max_count))
            self.connection.commit()
        except Exception as e:
            logger.error(f'添加中医特色治疗时间段失败: {e}')

    # 保持向后兼容性的方法
    def add_time_slot(self, label, max_count):
        # 默认添加到盆底康复时间段，保持向后兼容
        self.add_pelvic_time_slot(label, max_count)

    def update_pelvic_time_slot(self, slot_id, label=None, max_count=None):
        try:
            # 构建动态更新语句
            update_fields = []
            params = []

            if label is not None:
                update_fields.append("label=%s")
                params.append(label)

            if max_count is not None:
                update_fields.append("max_count=%s")
                params.append(max_count)

            if not update_fields:
                return  # 没有要更新的字段

            params.append(slot_id)
            query = f"UPDATE pelvic_time_slots SET {', '.join(update_fields)} WHERE id=%s"
            self.cursor.execute(query, params)
            self.connection.commit()
        except Exception as e:
            logger.error(f'更新盆底康复时间段失败: {e}')

    def update_tcm_time_slot(self, slot_id, label=None, max_count=None):
        try:
            # 构建动态更新语句
            update_fields = []
            params = []

            if label is not None:
                update_fields.append("label=%s")
                params.append(label)

            if max_count is not None:
                update_fields.append("max_count=%s")
                params.append(max_count)

            if not update_fields:
                return  # 没有要更新的字段

            params.append(slot_id)
            query = f"UPDATE tcm_time_slots SET {', '.join(update_fields)} WHERE id=%s"
            self.cursor.execute(query, params)
            self.connection.commit()
        except Exception as e:
            logger.error(f'更新中医特色治疗时间段失败: {e}')

    # 保持向后兼容性的方法
    def update_time_slot(self, slot_id, label=None, max_count=None):
        # 默认更新盆底康复时间段，保持向后兼容
        self.update_pelvic_time_slot(slot_id, label, max_count)

    def delete_pelvic_time_slot(self, slot_id):
        try:
            self.cursor.execute("DELETE FROM pelvic_time_slots WHERE id=%s", (slot_id,))
            self.connection.commit()
        except Exception as e:
            logger.error(f'删除盆底康复时间段失败: {e}')

    def delete_tcm_time_slot(self, slot_id):
        try:
            self.cursor.execute("DELETE FROM tcm_time_slots WHERE id=%s", (slot_id,))
            self.connection.commit()
        except Exception as e:
            logger.error(f'删除中医特色治疗时间段失败: {e}')

    def set_pelvic_time_slot_enabled(self, slot_id, enabled):
        try:
            self.cursor.execute("UPDATE pelvic_time_slots SET enabled=%s WHERE id=%s", (enabled, slot_id))
            self.connection.commit()
        except Exception as e:
            logger.error(f'设置盆底康复时间段启用状态失败: {e}')

    def set_tcm_time_slot_enabled(self, slot_id, enabled):
        try:
            self.cursor.execute("UPDATE tcm_time_slots SET enabled=%s WHERE id=%s", (enabled, slot_id))
            self.connection.commit()
        except Exception as e:
            logger.error(f'设置中医特色治疗时间段启用状态失败: {e}')

    # 保持向后兼容性的方法
    def delete_time_slot(self, slot_id):
        # 默认删除盆底康复时间段，保持向后兼容
        self.delete_pelvic_time_slot(slot_id)

    def set_time_slot_enabled(self, slot_id, enabled):
        # 默认设置盆底康复时间段，保持向后兼容
        self.set_pelvic_time_slot_enabled(slot_id, enabled)

    def get_daily_patients(self, query_date):
        try:
            query = """
                SELECT id, name, phone, appointment_date, time_slot, IFNULL(status, '未到') as status
                FROM pelvic_rehab
                WHERE appointment_date = %s
                ORDER BY time_slot ASC, id ASC
            """
            self.cursor.execute(query, (query_date,))
            results = self.cursor.fetchall()
            return [
                {
                    'id': row[0],
                    'name': row[1],
                    'phone': row[2],
                    'date': row[3].strftime('%Y-%m-%d'),
                    'time_slot': row[4],
                    'status': row[5]
                }
                for row in results
            ]
        except Exception as e:
            logger.error(f'获取每日预约病人失败: {e}')
            return []

    def get_setting(self, key, default=None):
        try:
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    `key` VARCHAR(64) PRIMARY KEY,
                    `value` VARCHAR(255) NOT NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局设置表';
            """)
            self.cursor.execute("SELECT value FROM settings WHERE `key`=%s", (key,))
            row = self.cursor.fetchone()
            return int(row[0]) if row else default
        except Exception as e:
            logger.error(f'获取设置失败: {e}')
            return default

    def set_setting(self, key, value):
        try:
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    `key` VARCHAR(64) PRIMARY KEY,
                    `value` VARCHAR(255) NOT NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局设置表';
            """)
            self.cursor.execute("REPLACE INTO settings (`key`, `value`) VALUES (%s, %s)", (key, str(value)))
            self.connection.commit()
        except Exception as e:
            logger.error(f'设置参数失败: {e}')


