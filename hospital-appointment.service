[Unit]
Description=Hospital Appointment System (Optimized)
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/var/www/hospital-appointment
Environment=PATH=/var/www/hospital-appointment/venv/bin
Environment=PYTHONPATH=/var/www/hospital-appointment
ExecStart=/var/www/hospital-appointment/venv/bin/gunicorn --config gunicorn_config.py app_optimized:app
Restart=always
RestartSec=3
TimeoutStartSec=60
TimeoutStopSec=30

# 健康检查
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
KillSignal=SIGTERM

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
