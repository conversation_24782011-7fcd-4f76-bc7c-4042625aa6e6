import pandas as pd
from io import BytesIO

def get_all_appointments(db):
    return db.get_all_rehab_appointments()

def update_appointment_status(db, appointment_id, new_status):
    db.update_appointment_status(appointment_id, new_status)

def get_statistics(db):
    # 返回每个人的预约次数统计
    return db.get_rehab_statistics()

def export_appointments_to_excel(db):
    appointments = db.get_all_rehab_appointments()
    df = pd.DataFrame(appointments)
    output = BytesIO()
    df.to_excel(output, index=False)
    output.seek(0)
    return output

def get_time_slots(db):
    return db.get_pelvic_time_slots()

def add_time_slot(db, label, max_count):
    db.add_pelvic_time_slot(label, max_count)

def update_time_slot(db, slot_id, label=None, max_count=None):
    db.update_pelvic_time_slot(slot_id, label, max_count)

def delete_time_slot(db, slot_id):
    db.delete_pelvic_time_slot(slot_id)

def set_time_slot_enabled(db, slot_id, enabled):
    db.set_pelvic_time_slot_enabled(slot_id, enabled)

def get_course_threshold(db):
    """获取盆底康复疗程完成阈值（保持向后兼容）"""
    return db.get_setting('pelvic_course_threshold', 10)

def set_course_threshold(db, value):
    """设置盆底康复疗程完成阈值（保持向后兼容）"""
    db.set_setting('pelvic_course_threshold', value)

def get_pelvic_course_threshold(db):
    """获取盆底康复疗程完成阈值"""
    return db.get_setting('pelvic_course_threshold', 10)

def set_pelvic_course_threshold(db, value):
    """设置盆底康复疗程完成阈值"""
    db.set_setting('pelvic_course_threshold', value)

def get_tcm_course_threshold(db):
    """获取中医特色治疗疗程完成阈值"""
    return db.get_setting('tcm_course_threshold', 8)

def set_tcm_course_threshold(db, value):
    """设置中医特色治疗疗程完成阈值"""
    db.set_setting('tcm_course_threshold', value)

def search_patients(db, keyword):
    """搜索患者信息"""
    return db.search_rehab_patients(keyword)

def get_patient_info(db, keyword):
    """获取患者详细信息"""
    return db.get_patient_detailed_info(keyword, 'rehab') 