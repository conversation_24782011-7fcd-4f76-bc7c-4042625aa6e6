from datetime import datetime, date, time

def validate_tcm_input(name, phone, appointment_date_str, time_slot, db):
    if not all([name, phone, appointment_date_str, time_slot]):
        raise ValueError("请填写所有必填项")
    if not phone.isdigit() or len(phone) != 11:
        raise ValueError("请输入正确的手机号码")

    # 从数据库获取有效的中医特色治疗时间段
    time_slots = db.get_tcm_time_slots()
    valid_time_slots = [slot['label'] for slot in time_slots if slot['enabled']]

    if time_slot not in valid_time_slots:
        raise ValueError("请选择有效的时间段")

    try:
        appointment_date = datetime.strptime(appointment_date_str, '%Y-%m-%d').date()
    except ValueError:
        raise ValueError("日期格式不正确")

    # 验证日期不能是过去的日期
    today = date.today()
    if appointment_date < today:
        raise ValueError("不能预约过去的日期，请选择今天或未来的日期")

    # 如果是今天，验证时间段是否已经过去
    if appointment_date == today:
        current_time = datetime.now().time()

        # 解析时间段，提取结束时间
        time_slot_end_time = _parse_time_slot_end_time(time_slot)

        if time_slot_end_time and current_time >= time_slot_end_time:
            raise ValueError(f"时间段 {time_slot} 已经过去，请选择其他时间段")

    return appointment_date

def _parse_time_slot_end_time(time_slot):
    """
    解析时间段字符串，提取结束时间
    例如：'上午08:00-09:00' -> time(9, 0)
    """
    try:
        # 移除中文前缀（上午、下午等）
        time_part = time_slot
        for prefix in ['上午', '下午', '晚上']:
            time_part = time_part.replace(prefix, '')

        # 查找时间范围分隔符
        if '-' in time_part:
            # 格式：08:00-09:00
            start_str, end_str = time_part.split('-')
            end_str = end_str.strip()

            # 解析结束时间
            if ':' in end_str:
                hour, minute = map(int, end_str.split(':'))
                return time(hour, minute)
        elif '：' in time_part:
            # 处理中文冒号
            time_part = time_part.replace('：', ':')
            if '-' in time_part:
                start_str, end_str = time_part.split('-')
                end_str = end_str.strip()
                if ':' in end_str:
                    hour, minute = map(int, end_str.split(':'))
                    return time(hour, minute)
    except (ValueError, IndexError):
        # 如果解析失败，返回None，不进行时间验证
        pass

    return None

def get_tcm_time_slot_limit(time_slot, db):
    # 从数据库获取中医特色治疗时间段限制
    time_slots = db.get_tcm_time_slots()
    for slot in time_slots:
        if slot['label'] == time_slot and slot['enabled']:
            return slot['max_count']
    # 默认限制3人
    return 3
