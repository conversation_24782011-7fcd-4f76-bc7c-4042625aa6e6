import pandas as pd
from io import BytesIO

def get_all_tcm_appointments(db):
    return db.get_all_tcm_appointments()

def update_tcm_appointment_status(db, appointment_id, new_status):
    db.update_tcm_appointment_status(appointment_id, new_status)

def get_tcm_statistics(db):
    # 返回每个人的中医预约次数统计
    return db.get_tcm_statistics()

def export_tcm_appointments_to_excel(db):
    appointments = db.get_all_tcm_appointments()
    df = pd.DataFrame(appointments)
    output = BytesIO()
    df.to_excel(output, index=False)
    output.seek(0)
    return output

def get_tcm_time_slots(db):
    return db.get_tcm_time_slots()

def add_tcm_time_slot(db, label, max_count):
    db.add_tcm_time_slot(label, max_count)

def update_tcm_time_slot(db, slot_id, label=None, max_count=None):
    db.update_tcm_time_slot(slot_id, label, max_count)

def delete_tcm_time_slot(db, slot_id):
    db.delete_tcm_time_slot(slot_id)

def set_tcm_time_slot_enabled(db, slot_id, enabled):
    db.set_tcm_time_slot_enabled(slot_id, enabled)

def search_tcm_patients(db, keyword):
    """搜索中医患者信息"""
    return db.search_tcm_patients(keyword)

def get_tcm_patient_info(db, keyword):
    """获取中医患者详细信息"""
    return db.get_patient_detailed_info(keyword, 'tcm')

def get_tcm_course_threshold(db):
    """获取中医特色治疗疗程完成阈值"""
    return db.get_setting('tcm_course_threshold', 8)

def set_tcm_course_threshold(db, value):
    """设置中医特色治疗疗程完成阈值"""
    db.set_setting('tcm_course_threshold', value)
