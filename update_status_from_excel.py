#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从Excel文件更新预约状态脚本
根据Excel表中"检查完成"列的值(1=已就诊, 0=未到)更新数据库中的预约状态
"""

import pandas as pd
from db_connection import Database
import logging
import sys
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_status.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def read_excel_data(excel_file_path):
    """读取Excel文件数据"""
    try:
        # 读取Excel文件
        df = pd.read_excel('当天预约人员.xlsx')
        
        # 检查必要的列是否存在
        required_columns = ['编号', '姓名', '电话', '预约日期', '预约时间', '检查完成']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            logger.error(f"Excel文件缺少必要的列: {missing_columns}")
            return None
            
        logger.info(f"成功读取Excel文件，共 {len(df)} 行数据")
        
        # 显示前几行数据供确认
        logger.info("Excel数据预览:")
        logger.info(df.head().to_string())
        
        return df
        
    except Exception as e:
        logger.error(f"读取Excel文件失败: {e}")
        return None

def update_appointment_status_from_excel(df):
    """根据Excel数据更新预约状态"""
    updated_count = 0
    error_count = 0
    
    try:
        with Database() as db:
            for index, row in df.iterrows():
                try:
                    # 获取数据
                    appointment_id = str(row['编号']).strip()
                    name = str(row['姓名']).strip()
                    phone = str(row['电话']).strip()
                    check_status = row['检查完成']
                    
                    # 根据检查完成状态确定新状态
                    if check_status == 1:
                        new_status = '已就诊'
                    elif check_status == 0:
                        new_status = '未到'
                    else:
                        logger.warning(f"行 {index+1}: 检查完成状态值无效 ({check_status})，跳过")
                        continue
                    
                    # 首先检查预约是否存在
                    check_query = """
                        SELECT id, name, phone, status 
                        FROM pelvic_rehab 
                        WHERE id = %s
                    """
                    db.cursor.execute(check_query, (appointment_id,))
                    result = db.cursor.fetchone()
                    
                    if not result:
                        logger.warning(f"行 {index+1}: 未找到编号为 {appointment_id} 的预约记录")
                        error_count += 1
                        continue
                    
                    current_id, current_name, current_phone, current_status = result
                    
                    # 验证姓名和电话是否匹配（可选验证）
                    if current_name != name:
                        logger.warning(f"行 {index+1}: 姓名不匹配 - Excel: {name}, 数据库: {current_name}")
                    
                    # 如果状态已经正确，跳过更新
                    if current_status == new_status:
                        logger.debug(f"行 {index+1}: 编号 {appointment_id} 状态已经是 {new_status}，跳过")
                        continue
                    
                    # 更新状态
                    update_query = "UPDATE pelvic_rehab SET status = %s WHERE id = %s"
                    db.cursor.execute(update_query, (new_status, appointment_id))
                    
                    logger.info(f"行 {index+1}: 更新编号 {appointment_id} ({name}) 状态: {current_status} -> {new_status}")
                    updated_count += 1
                    
                except Exception as e:
                    logger.error(f"行 {index+1}: 更新失败 - {e}")
                    error_count += 1
            
            # 提交所有更改
            db.connection.commit()
            
    except Exception as e:
        logger.error(f"数据库操作失败: {e}")
        return False
    
    logger.info(f"更新完成: 成功更新 {updated_count} 条记录，错误 {error_count} 条")
    return True

def main():
    """主函数"""
    print("=== 从Excel更新预约状态脚本 ===")
    
    # 获取Excel文件路径
    if len(sys.argv) > 1:
        excel_file = sys.argv[1]
    else:
        excel_file = input("请输入Excel文件路径: ").strip()
    
    # 检查文件是否存在
    if not os.path.exists(excel_file):
        logger.error(f"文件不存在: {excel_file}")
        return
    
    # 确认操作
    print(f"\n将要处理的Excel文件: {excel_file}")
    print("状态映射规则:")
    print("  检查完成 = 1 -> 已就诊")
    print("  检查完成 = 0 -> 未到")
    
    confirm = input("\n确认要继续吗? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes', '是']:
        print("操作已取消")
        return
    
    # 读取Excel数据
    logger.info("开始读取Excel文件...")
    df = read_excel_data(excel_file)
    
    if df is None:
        logger.error("读取Excel文件失败，程序退出")
        return
    
    # 更新数据库状态
    logger.info("开始更新数据库状态...")
    success = update_appointment_status_from_excel(df)
    
    if success:
        logger.info("✅ 状态更新完成！")
    else:
        logger.error("❌ 状态更新失败！")

if __name__ == "__main__":
    main() 