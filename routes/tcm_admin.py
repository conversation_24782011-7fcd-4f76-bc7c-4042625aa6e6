from flask import Blueprint, render_template, request, redirect, url_for, send_file, jsonify
from db_connection import Database
from services.tcm_admin_service import get_all_tcm_appointments, update_tcm_appointment_status, get_tcm_statistics, export_tcm_appointments_to_excel, \
    get_tcm_time_slots, add_tcm_time_slot, update_tcm_time_slot, delete_tcm_time_slot, set_tcm_time_slot_enabled, \
    search_tcm_patients, get_tcm_patient_info, get_tcm_course_threshold, set_tcm_course_threshold
import io
from datetime import date

tcm_admin_bp = Blueprint('tcm_admin', __name__, url_prefix='/tcm-admin')

@tcm_admin_bp.route('/')
def tcm_admin_index():
    with Database() as db:
        appointments = get_all_tcm_appointments(db)
        stats = get_tcm_statistics(db)
        tcm_course_threshold = get_tcm_course_threshold(db)
    return render_template('tcm-admin.html', appointments=appointments, stats=stats,
                         tcm_course_threshold=tcm_course_threshold)

@tcm_admin_bp.route('/update-status', methods=['POST'])
def update_status():
    appointment_id = request.form.get('appointment_id')
    new_status = request.form.get('status')
    with Database() as db:
        update_tcm_appointment_status(db, appointment_id, new_status)
    return redirect(url_for('tcm_admin.tcm_admin_index'))

@tcm_admin_bp.route('/delete', methods=['POST'])
def delete_appointment():
    appointment_id = request.form.get('appointment_id')
    with Database() as db:
        db.delete_tcm_appointment(appointment_id)
    return redirect(url_for('tcm_admin.tcm_admin_index'))

@tcm_admin_bp.route('/export')
def export():
    with Database() as db:
        output = export_tcm_appointments_to_excel(db)
    return send_file(output, as_attachment=True, download_name='tcm_appointments.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

@tcm_admin_bp.route('/daily-patients')
def daily_patients_page():
    query_date = request.args.get('date') or date.today().strftime('%Y-%m-%d')
    with Database() as db:
        appointments = db.get_daily_tcm_patients(query_date)
    return render_template('tcm-daily-patients.html', appointments=appointments, query_date=query_date)

@tcm_admin_bp.route('/update-patient-status', methods=['POST'])
def update_patient_status():
    appointment_id = request.form.get('appointment_id')
    new_status = request.form.get('status')
    with Database() as db:
        db.update_tcm_appointment_status(appointment_id, new_status)
    return '', 204

@tcm_admin_bp.route('/time-slots', methods=['GET'])
def api_get_tcm_time_slots():
    with Database() as db:
        slots = get_tcm_time_slots(db)
    return jsonify({'success': True, 'data': slots})

@tcm_admin_bp.route('/time-slots', methods=['POST'])
def api_add_tcm_time_slot():
    data = request.json
    label = data.get('label')
    max_count = data.get('max_count', 3)
    with Database() as db:
        add_tcm_time_slot(db, label, max_count)
    return jsonify({'success': True})

@tcm_admin_bp.route('/time-slots/<int:slot_id>', methods=['PUT'])
def api_update_tcm_time_slot(slot_id):
    data = request.json
    label = data.get('label')
    max_count = data.get('max_count')

    # 只传递非空的字段
    kwargs = {}
    if label is not None:
        kwargs['label'] = label
    if max_count is not None:
        kwargs['max_count'] = int(max_count)

    with Database() as db:
        update_tcm_time_slot(db, slot_id, **kwargs)
    return jsonify({'success': True})

@tcm_admin_bp.route('/time-slots/<int:slot_id>', methods=['DELETE'])
def api_delete_tcm_time_slot(slot_id):
    with Database() as db:
        delete_tcm_time_slot(db, slot_id)
    return jsonify({'success': True})

@tcm_admin_bp.route('/time-slots/<int:slot_id>/enabled', methods=['POST'])
def api_set_tcm_time_slot_enabled(slot_id):
    data = request.json
    enabled = data.get('enabled', True)
    with Database() as db:
        set_tcm_time_slot_enabled(db, slot_id, enabled)
    return jsonify({'success': True})

@tcm_admin_bp.route('/search', methods=['GET', 'POST'])
def search_tcm_patients_page():
    """中医患者搜索页面"""
    if request.method == 'POST':
        keyword = request.form.get('keyword', '').strip()
        if keyword:
            with Database() as db:
                patient_info = get_tcm_patient_info(db, keyword)
                tcm_course_threshold = get_tcm_course_threshold(db)
            return render_template('patient_search.html',
                                   patient_info=patient_info,
                                   keyword=keyword,
                                   tcm_course_threshold=tcm_course_threshold,
                                   treatment_type='tcm')
    return render_template('patient_search.html', patient_info=None, keyword='', treatment_type='tcm')

@tcm_admin_bp.route('/api/search', methods=['POST'])
def api_search_tcm_patients():
    """API搜索中医患者"""
    data = request.get_json()
    keyword = data.get('keyword', '').strip()
    
    if not keyword:
        return jsonify({'success': False, 'message': '请输入搜索关键词'})
    
    with Database() as db:
        results = search_tcm_patients(db, keyword)
    
    return jsonify({'success': True, 'results': results})

@tcm_admin_bp.route('/tcm-course-threshold', methods=['POST'])
def api_set_tcm_course_threshold():
    """设置中医特色治疗疗程完成阈值"""
    value = int(request.form.get('value', 8))
    with Database() as db:
        set_tcm_course_threshold(db, value)
    return '', 204
