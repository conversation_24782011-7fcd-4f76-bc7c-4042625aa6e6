from flask import Blueprint, render_template, request
from services.rehab_service import validate_rehab_input, get_time_slot_limit
from db_connection import Database
import logging

rehab_bp = Blueprint('rehab', __name__)
logger = logging.getLogger(__name__)

@rehab_bp.route('/')
def menu():
    return render_template('menu.html')

@rehab_bp.route('/pelvic-rehab')
def pelvic_rehab():
    with Database() as db:
        time_slots = db.get_pelvic_time_slots()
        # 现在数据库方法确保返回字典格式
        enabled_slots = [slot for slot in time_slots if slot['enabled']]
    return render_template('pelvic-rehab.html', time_slots=enabled_slots)

@rehab_bp.route('/submit-rehab', methods=['POST'])
def submit_rehab():
    try:
        name = request.form.get('name')
        phone = request.form.get('phone')
        appointment_date_str = request.form.get('date')
        time_slot = request.form.get('time_slot')

        logger.debug(f"接收到盆底康复预约请求: {name}, {appointment_date_str}, {time_slot}")

        with Database() as db:
            try:
                appointment_date = validate_rehab_input(name, phone, appointment_date_str, time_slot, db)
                appointment_count = db.get_appointment_count('pelvic_rehab', appointment_date, time_slot)
                limit = get_time_slot_limit(time_slot, db)
                if appointment_count >= limit:
                    return render_template('verify.html', message="预约已满，请选择其他时间")
                success, operation_type, daily_number = db.add_rehab_appointment(
                    name, phone, appointment_date, time_slot
                )
                if success:
                    return render_template(
                        'success.html',
                        name=name,
                        date=appointment_date.strftime('%Y年%m月%d日'),
                        time_slot=time_slot,
                        operation_type=operation_type,
                        daily_number=daily_number
                    )
                else:
                    return render_template('verify.html', message="预约失败，请稍后重试")
            except Exception as e:
                logger.error(f"数据库操作时发生错误: {e}")
                return render_template('verify.html', message="系统错误，请稍后重试")
    except Exception as e:
        logger.error(f"处理预约时发生错误: {e}")
        return render_template('verify.html', message="系统错误，请稍后重试")

@rehab_bp.route('/query-rehab', methods=['POST'])
def query_rehab():
    try:
        phone = request.form.get('query_phone')
        if not phone or not phone.isdigit() or len(phone) != 11:
            return render_template('verify.html', message="请输入正确的11位手机号码")
        with Database() as db:
            appointments = db.get_rehab_appointments(phone)
            if appointments is None:
                return render_template('verify.html', message="查询失败，请稍后重试")
            if not appointments:
                return render_template('verify.html', message="未找到预约记录")
            return render_template('query-result.html', appointments=appointments)
    except Exception as e:
        logger.error(f"查询预约记录时发生错误: {e}")
        return render_template('verify.html', message="系统错误，请稍后重试") 