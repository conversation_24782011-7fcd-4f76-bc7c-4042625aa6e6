from flask import Blueprint, render_template, request, redirect, url_for, send_file, jsonify
from db_connection import Database
from services.admin_service import get_all_appointments, update_appointment_status, get_statistics, export_appointments_to_excel, \
    get_time_slots, add_time_slot, update_time_slot, delete_time_slot, set_time_slot_enabled, \
    get_course_threshold, set_course_threshold, get_pelvic_course_threshold, set_pelvic_course_threshold, \
    search_patients, get_patient_info
import io
from datetime import date

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/')
def admin_index():
    with Database() as db:
        appointments = get_all_appointments(db)
        stats = get_statistics(db)
        pelvic_course_threshold = get_pelvic_course_threshold(db)
    return render_template('admin.html', appointments=appointments, stats=stats,
                         pelvic_course_threshold=pelvic_course_threshold)

@admin_bp.route('/update-status', methods=['POST'])
def update_status():
    appointment_id = request.form.get('appointment_id')
    new_status = request.form.get('status')
    with Database() as db:
        update_appointment_status(db, appointment_id, new_status)
    return redirect(url_for('admin.admin_index'))

@admin_bp.route('/delete', methods=['POST'])
def delete_appointment():
    appointment_id = request.form.get('appointment_id')
    with Database() as db:
        db.delete_appointment(appointment_id)
    return redirect(url_for('admin.admin_index'))

@admin_bp.route('/export')
def export():
    with Database() as db:
        output = export_appointments_to_excel(db)
    return send_file(output, as_attachment=True, download_name='appointments.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

@admin_bp.route('/time-slots', methods=['GET'])
def api_get_time_slots():
    with Database() as db:
        slots = get_time_slots(db)
    return jsonify({'success': True, 'data': slots})

@admin_bp.route('/time-slots', methods=['POST'])
def api_add_time_slot():
    data = request.json
    label = data.get('label')
    max_count = data.get('max_count', 8)
    with Database() as db:
        add_time_slot(db, label, max_count)
    return jsonify({'success': True})

@admin_bp.route('/time-slots/<int:slot_id>', methods=['PUT'])
def api_update_time_slot(slot_id):
    data = request.json
    label = data.get('label')
    max_count = data.get('max_count')

    # 只传递非空的字段
    kwargs = {}
    if label is not None:
        kwargs['label'] = label
    if max_count is not None:
        kwargs['max_count'] = int(max_count)

    with Database() as db:
        update_time_slot(db, slot_id, **kwargs)
    return jsonify({'success': True})

@admin_bp.route('/time-slots/<int:slot_id>', methods=['DELETE'])
def api_delete_time_slot(slot_id):
    with Database() as db:
        delete_time_slot(db, slot_id)
    return jsonify({'success': True})

@admin_bp.route('/time-slots/<int:slot_id>/enabled', methods=['POST'])
def api_set_time_slot_enabled(slot_id):
    data = request.json
    enabled = data.get('enabled', True)
    with Database() as db:
        set_time_slot_enabled(db, slot_id, enabled)
    return jsonify({'success': True})

@admin_bp.route('/daily-patients')
def daily_patients_page():
    query_date = request.args.get('date') or date.today().strftime('%Y-%m-%d')
    with Database() as db:
        appointments = db.get_daily_patients(query_date)
    return render_template('daily_patients.html', appointments=appointments, query_date=query_date)

@admin_bp.route('/update-patient-status', methods=['POST'])
def update_patient_status():
    appointment_id = request.form.get('appointment_id')
    new_status = request.form.get('status')
    with Database() as db:
        db.update_appointment_status(appointment_id, new_status)
    return '', 204

@admin_bp.route('/course-threshold', methods=['POST'])
def api_set_course_threshold():
    """设置盆底康复疗程完成阈值（保持向后兼容）"""
    value = int(request.form.get('value', 10))
    with Database() as db:
        set_pelvic_course_threshold(db, value)
    return '', 204

@admin_bp.route('/pelvic-course-threshold', methods=['POST'])
def api_set_pelvic_course_threshold():
    """设置盆底康复疗程完成阈值"""
    value = int(request.form.get('value', 10))
    with Database() as db:
        set_pelvic_course_threshold(db, value)
    return '', 204

@admin_bp.route('/search', methods=['GET', 'POST'])
def search_patients_page():
    """患者搜索页面"""
    if request.method == 'POST':
        keyword = request.form.get('keyword', '').strip()
        if keyword:
            with Database() as db:
                patient_info = get_patient_info(db, keyword)
                pelvic_course_threshold = get_pelvic_course_threshold(db)
            return render_template('patient_search.html',
                                   patient_info=patient_info,
                                   keyword=keyword,
                                   pelvic_course_threshold=pelvic_course_threshold,
                                   treatment_type='rehab')
    return render_template('patient_search.html', patient_info=None, keyword='', treatment_type='rehab')

@admin_bp.route('/api/search', methods=['POST'])
def api_search_patients():
    """API搜索患者"""
    data = request.get_json()
    keyword = data.get('keyword', '').strip()
    
    if not keyword:
        return jsonify({'success': False, 'message': '请输入搜索关键词'})
    
    with Database() as db:
        results = search_patients(db, keyword)
    
    return jsonify({'success': True, 'results': results}) 