import multiprocessing

# 基本配置
bind = "0.0.0.0:5010"
workers = min(multiprocessing.cpu_count() * 2 + 1, 4)
worker_class = 'sync'
timeout = 60
keepalive = 2

# 日志配置
accesslog = "/var/log/gunicorn/access.log"
errorlog = "/var/log/gunicorn/error.log"
loglevel = "info"

# 进程配置
user = "www-data"
group = "www-data"
daemon = False
pidfile = "/var/run/gunicorn.pid"

# 性能配置
max_requests = 1000
max_requests_jitter = 50
preload_app = True
