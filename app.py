from flask import Flask, request, g
from routes.rehab import rehab_bp
from routes.admin import admin_bp
from routes.tcm import tcm_bp
from routes.tcm_admin import tcm_admin_bp
import time
import logging

# 尝试导入性能监控，如果失败则跳过
try:
    from performance_monitor import monitor
    PERFORMANCE_MONITORING = True
except ImportError:
    PERFORMANCE_MONITORING = False
    print("性能监控模块未找到，跳过性能监控功能")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

app = Flask(__name__)

# Flask性能优化配置
app.config.update(
    # 禁用调试模式
    DEBUG=False,
    # 启用JSON排序
    JSON_SORT_KEYS=False,
    # 设置最大内容长度
    MAX_CONTENT_LENGTH=16 * 1024 * 1024,  # 16MB
    # 启用压缩
    COMPRESS_MIMETYPES=[
        'text/html', 'text/css', 'text/xml', 'application/json',
        'application/javascript', 'text/javascript'
    ]
)

# 性能监控中间件
@app.before_request
def before_request():
    g.start_time = time.time()

@app.after_request
def after_request(response):
    # 添加基本的响应头
    if hasattr(g, 'start_time'):
        duration = time.time() - g.start_time
        response.headers['X-Response-Time'] = f"{duration:.3f}s"

        # 如果性能监控可用，记录请求时间
        if PERFORMANCE_MONITORING:
            monitor.record_request_time(request.endpoint or 'unknown', duration)

    # 添加缓存控制头
    if request.endpoint and 'static' in request.endpoint:
        response.headers['Cache-Control'] = 'public, max-age=31536000'  # 1年
    else:
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'

    return response

# 状态和性能监控路由
@app.route('/status')
def status():
    """获取应用状态"""
    return {
        'status': 'ok',
        'timestamp': time.time(),
        'message': '应用程序正常运行',
        'performance_monitoring': PERFORMANCE_MONITORING
    }

@app.route('/performance-stats')
def performance_stats():
    """获取性能统计信息"""
    if not PERFORMANCE_MONITORING:
        return {'error': '性能监控未启用'}, 503

    stats = monitor.get_stats()
    return {
        'performance': stats,
        'timestamp': time.time()
    }

# 测试路由
@app.route('/test')
def test():
    """测试路由"""
    return {'status': 'ok', 'message': '应用程序正常运行'}

# 注册蓝图
app.register_blueprint(rehab_bp)
app.register_blueprint(admin_bp)
app.register_blueprint(tcm_bp)
app.register_blueprint(tcm_admin_bp)

if __name__ == '__main__':
    # 生产环境配置
    app.run(host='0.0.0.0', port=5010, debug=False)
