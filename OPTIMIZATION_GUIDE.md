# 医院预约系统优化指南

## 优化概述

本次优化主要包括以下几个方面：

### 1. 数据库连接池优化
- 实现了数据库连接池，减少连接创建开销
- 支持连接复用和自动清理
- 提高并发性能

### 2. 缓存系统
- 支持Redis缓存（优先）和内存缓存（备选）
- 缓存常用查询结果，减少数据库访问
- 自动缓存失效和更新

### 3. 静态资源优化
- 启用Gzip压缩
- 添加缓存控制头
- 优化静态文件服务

### 4. 疗程完成阈值分离
- 盆底康复和中医特色治疗独立阈值设置
- 支持不同治疗类型的个性化配置

## 安装和部署

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

新增的依赖包：
- `redis>=4.0.0` - Redis缓存支持
- `DBUtils>=3.0.0` - 数据库连接池工具
- `Flask-Caching>=2.0.0` - Flask缓存扩展

### 2. 可选：安装Redis（推荐）

```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# CentOS/RHEL
sudo yum install redis

# macOS
brew install redis
```

启动Redis服务：
```bash
sudo systemctl start redis
# 或
redis-server
```

### 3. 数据迁移

运行迁移脚本确保数据安全：

```bash
python migrate_to_optimized.py
```

### 4. 功能测试

运行测试脚本验证所有功能：

```bash
python test_optimization.py
```

### 5. 启动优化版本

```bash
python app_optimized.py
```

## 配置说明

### 数据库连接池配置

在 `db_pool.py` 中可以调整以下参数：

```python
DatabasePool(
    min_connections=5,      # 最小连接数
    max_connections=20,     # 最大连接数
    max_idle_time=300,      # 最大空闲时间（秒）
    connection_timeout=10   # 连接超时时间（秒）
)
```

### 缓存配置

在 `cache_manager.py` 中可以调整：

```python
MemoryCache(
    default_timeout=300,    # 默认缓存时间（秒）
    max_size=1000          # 最大缓存项数
)
```

### Redis配置

如果使用Redis，可以在 `cache_manager.py` 中修改连接参数：

```python
redis.Redis(
    host='localhost',
    port=6379,
    db=0
)
```

## 新功能使用

### 1. 独立阈值设置

#### 盆底康复疗程阈值
- 管理界面：`/admin` 页面中的"盆底康复疗程完成阈值"设置
- API接口：`POST /admin/pelvic-course-threshold`

#### 中医特色治疗疗程阈值
- 管理界面：`/tcm-admin` 页面中的"中医特色治疗疗程完成阈值"设置
- API接口：`POST /tcm-admin/tcm-course-threshold`

### 2. 性能监控

访问以下URL查看系统状态：

- `/status` - 应用状态和统计信息
- `/health` - 健康检查
- `/performance-stats` - 性能统计（需要performance_monitor模块）

## 性能改进

### 预期性能提升

1. **数据库查询性能**：提升30-50%
2. **并发处理能力**：提升2-3倍
3. **响应时间**：减少20-40%
4. **内存使用**：优化15-25%

### 缓存策略

- 预约列表：缓存5分钟
- 统计数据：缓存10分钟
- 时间段配置：缓存5分钟
- 设置参数：缓存1小时

## 监控和维护

### 1. 日志监控

查看应用日志：
```bash
tail -f app.log
```

### 2. 数据库连接池监控

访问 `/status` 接口查看连接池状态：
```json
{
  "database_pool": {
    "total_connections": 10,
    "active_connections": 2,
    "idle_connections": 8,
    "max_connections": 20,
    "min_connections": 5
  }
}
```

### 3. 缓存监控

查看缓存状态：
```json
{
  "cache": {
    "type": "redis",
    "used_memory": "2.5M",
    "connected_clients": 1
  }
}
```

## 故障排除

### 1. Redis连接失败

如果Redis不可用，系统会自动降级到内存缓存：
```
WARNING: Redis连接失败，使用内存缓存
```

### 2. 数据库连接池满

如果出现连接池满的警告：
```
WARNING: 连接池已满，等待连接释放...
```

可以增加 `max_connections` 参数。

### 3. 缓存失效

如果需要清除所有缓存：
```python
from cache_manager import get_cache_manager
cache = get_cache_manager()
cache.clear_pattern('*')
```

## 回滚方案

如果需要回滚到原版本：

1. 停止优化版本应用
2. 启动原版本应用：`python app.py`
3. 数据会保持完整，因为数据库结构兼容

## 注意事项

1. **数据安全**：所有优化都保持数据库结构兼容，不会丢失数据
2. **向后兼容**：保留了原有的API接口
3. **渐进式升级**：可以逐步启用各项优化功能
4. **监控重要**：建议定期检查性能指标和错误日志

## 技术支持

如有问题，请检查：
1. 依赖包是否正确安装
2. 数据库连接是否正常
3. Redis服务是否运行（可选）
4. 日志文件中的错误信息
