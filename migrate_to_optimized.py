#!/usr/bin/env python3
"""
数据库迁移脚本：从原始数据库结构迁移到优化版本
确保数据安全和功能完整性
"""

import logging
import sys
from db_connection import Database as OriginalDatabase
from db_connection_optimized import OptimizedDatabase

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def migrate_settings():
    """迁移设置数据"""
    logger.info("开始迁移设置数据...")
    
    try:
        with OriginalDatabase() as old_db:
            with OptimizedDatabase() as new_db:
                # 获取原有的疗程阈值设置
                old_threshold = old_db.get_setting('course_threshold', 10)
                
                # 设置盆底康复疗程阈值（使用原有值）
                new_db.set_setting('pelvic_course_threshold', old_threshold)
                logger.info(f"盆底康复疗程阈值设置为: {old_threshold}")
                
                # 设置中医特色治疗疗程阈值（默认值8）
                tcm_threshold = new_db.get_setting('tcm_course_threshold', 8)
                if tcm_threshold == 8:  # 如果是默认值，则设置
                    new_db.set_setting('tcm_course_threshold', 8)
                    logger.info(f"中医特色治疗疗程阈值设置为: 8")
                else:
                    logger.info(f"中医特色治疗疗程阈值已存在: {tcm_threshold}")
                
        logger.info("设置数据迁移完成")
        return True
        
    except Exception as e:
        logger.error(f"迁移设置数据失败: {e}")
        return False

def verify_data_integrity():
    """验证数据完整性"""
    logger.info("开始验证数据完整性...")
    
    try:
        with OriginalDatabase() as old_db:
            with OptimizedDatabase() as new_db:
                # 验证盆底康复预约数据
                old_rehab_count = len(old_db.get_all_rehab_appointments())
                new_rehab_count = len(new_db.get_all_rehab_appointments())
                
                if old_rehab_count == new_rehab_count:
                    logger.info(f"盆底康复预约数据完整: {old_rehab_count} 条记录")
                else:
                    logger.warning(f"盆底康复预约数据不匹配: 原{old_rehab_count} vs 新{new_rehab_count}")
                
                # 验证中医特色治疗预约数据
                old_tcm_count = len(old_db.get_all_tcm_appointments())
                new_tcm_count = len(new_db.get_all_tcm_appointments())
                
                if old_tcm_count == new_tcm_count:
                    logger.info(f"中医特色治疗预约数据完整: {old_tcm_count} 条记录")
                else:
                    logger.warning(f"中医特色治疗预约数据不匹配: 原{old_tcm_count} vs 新{new_tcm_count}")
                
                # 验证时间段数据
                old_pelvic_slots = len(old_db.get_pelvic_time_slots())
                new_pelvic_slots = len(new_db.get_pelvic_time_slots())
                
                if old_pelvic_slots == new_pelvic_slots:
                    logger.info(f"盆底康复时间段数据完整: {old_pelvic_slots} 条记录")
                else:
                    logger.warning(f"盆底康复时间段数据不匹配: 原{old_pelvic_slots} vs 新{new_pelvic_slots}")
                
                old_tcm_slots = len(old_db.get_tcm_time_slots())
                new_tcm_slots = len(new_db.get_tcm_time_slots())
                
                if old_tcm_slots == new_tcm_slots:
                    logger.info(f"中医特色治疗时间段数据完整: {old_tcm_slots} 条记录")
                else:
                    logger.warning(f"中医特色治疗时间段数据不匹配: 原{old_tcm_slots} vs 新{new_tcm_slots}")
        
        logger.info("数据完整性验证完成")
        return True
        
    except Exception as e:
        logger.error(f"验证数据完整性失败: {e}")
        return False

def test_optimized_functions():
    """测试优化后的功能"""
    logger.info("开始测试优化后的功能...")
    
    try:
        with OptimizedDatabase() as db:
            # 测试设置功能
            test_value = 15
            db.set_setting('test_setting', test_value)
            retrieved_value = db.get_setting('test_setting', 0)
            
            if retrieved_value == test_value:
                logger.info("设置功能测试通过")
            else:
                logger.error(f"设置功能测试失败: 期望{test_value}, 实际{retrieved_value}")
                return False
            
            # 清理测试数据
            db._execute_query("DELETE FROM settings WHERE `key` = 'test_setting'")
            
            # 测试阈值功能
            pelvic_threshold = db.get_setting('pelvic_course_threshold', 10)
            tcm_threshold = db.get_setting('tcm_course_threshold', 8)
            
            logger.info(f"盆底康复疗程阈值: {pelvic_threshold}")
            logger.info(f"中医特色治疗疗程阈值: {tcm_threshold}")
            
            # 测试缓存功能
            from cache_manager import get_cache_manager
            cache = get_cache_manager()
            
            cache.set('test_key', 'test_value', 60)
            cached_value = cache.get('test_key')
            
            if cached_value == 'test_value':
                logger.info("缓存功能测试通过")
            else:
                logger.warning("缓存功能测试失败，但不影响核心功能")
            
            cache.delete('test_key')
            
        logger.info("优化功能测试完成")
        return True
        
    except Exception as e:
        logger.error(f"测试优化功能失败: {e}")
        return False

def test_performance():
    """测试性能改进"""
    logger.info("开始性能测试...")
    
    try:
        import time
        from db_pool import get_db_pool
        
        # 测试连接池
        pool = get_db_pool()
        stats = pool.get_stats()
        logger.info(f"连接池状态: {stats}")
        
        # 测试数据库查询性能
        start_time = time.time()
        
        with OptimizedDatabase() as db:
            # 执行一些查询
            db.get_all_rehab_appointments()
            db.get_all_tcm_appointments()
            db.get_pelvic_time_slots()
            db.get_tcm_time_slots()
        
        end_time = time.time()
        query_time = end_time - start_time
        
        logger.info(f"查询性能测试完成，耗时: {query_time:.3f}秒")
        
        # 测试缓存性能
        from cache_manager import get_cache_manager
        cache = get_cache_manager()
        cache_stats = cache.get_stats()
        logger.info(f"缓存状态: {cache_stats}")
        
        logger.info("性能测试完成")
        return True
        
    except Exception as e:
        logger.error(f"性能测试失败: {e}")
        return False

def main():
    """主迁移流程"""
    logger.info("开始数据库优化迁移...")
    
    success = True
    
    # 1. 迁移设置数据
    if not migrate_settings():
        success = False
    
    # 2. 验证数据完整性
    if not verify_data_integrity():
        success = False
    
    # 3. 测试优化功能
    if not test_optimized_functions():
        success = False
    
    # 4. 测试性能
    if not test_performance():
        success = False
    
    if success:
        logger.info("数据库优化迁移成功完成！")
        logger.info("现在可以使用 app_optimized.py 启动优化版本的应用")
        return 0
    else:
        logger.error("数据库优化迁移过程中出现错误，请检查日志")
        return 1

if __name__ == '__main__':
    sys.exit(main())
