import os
import gzip
import hashlib
import mimetypes
from flask import Flask, request, send_file, make_response
from functools import wraps
import logging

logger = logging.getLogger(__name__)

class StaticOptimizer:
    """静态资源优化器"""
    
    def __init__(self, app: Flask = None):
        self.app = app
        self.cache_dir = 'static_cache'
        self.gzip_types = {
            'text/html', 'text/css', 'text/javascript', 'application/javascript',
            'application/json', 'text/xml', 'application/xml', 'text/plain'
        }
        
        if app:
            self.init_app(app)
    
    def init_app(self, app: Flask):
        """初始化Flask应用"""
        self.app = app
        
        # 创建缓存目录
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # 注册静态文件处理器
        app.before_request(self.before_request)
        app.after_request(self.after_request)
    
    def before_request(self):
        """请求前处理"""
        # 检查是否支持gzip
        if request.headers.get('Accept-Encoding', '').find('gzip') != -1:
            request.supports_gzip = True
        else:
            request.supports_gzip = False
    
    def after_request(self, response):
        """请求后处理"""
        # 添加缓存控制头
        if request.endpoint and 'static' in request.endpoint:
            # 静态文件缓存1年
            response.headers['Cache-Control'] = 'public, max-age=31536000, immutable'
            response.headers['Expires'] = 'Thu, 31 Dec 2025 23:59:59 GMT'
        else:
            # 动态内容不缓存
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
        
        # 添加安全头
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        # 压缩响应
        if (hasattr(request, 'supports_gzip') and request.supports_gzip and
            response.content_type in self.gzip_types and
            len(response.data) > 1024):  # 只压缩大于1KB的内容
            
            response.data = gzip.compress(response.data)
            response.headers['Content-Encoding'] = 'gzip'
            response.headers['Content-Length'] = len(response.data)
        
        return response
    
    def get_file_hash(self, filepath):
        """获取文件哈希值"""
        try:
            with open(filepath, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()[:8]
        except:
            return None
    
    def compress_file(self, filepath):
        """压缩文件"""
        try:
            compressed_path = os.path.join(self.cache_dir, 
                                         os.path.basename(filepath) + '.gz')
            
            with open(filepath, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    f_out.write(f_in.read())
            
            return compressed_path
        except Exception as e:
            logger.error(f"压缩文件失败 {filepath}: {e}")
            return None

def enable_compression(app: Flask):
    """启用响应压缩"""
    
    @app.after_request
    def compress_response(response):
        # 检查是否支持gzip
        accept_encoding = request.headers.get('Accept-Encoding', '')
        
        if ('gzip' in accept_encoding and 
            response.status_code < 300 and
            response.content_type.startswith(('text/', 'application/json', 'application/javascript'))):
            
            # 只压缩大于1KB的响应
            if len(response.data) > 1024:
                response.data = gzip.compress(response.data)
                response.headers['Content-Encoding'] = 'gzip'
                response.headers['Content-Length'] = len(response.data)
                response.headers['Vary'] = 'Accept-Encoding'
        
        return response

def add_cache_headers(app: Flask):
    """添加缓存头"""
    
    @app.after_request
    def set_cache_headers(response):
        # 静态资源缓存策略
        if request.endpoint and 'static' in request.endpoint:
            # 静态文件缓存1年
            response.headers['Cache-Control'] = 'public, max-age=31536000, immutable'
            response.headers['ETag'] = f'"{hash(request.path)}"'
        else:
            # API响应缓存策略
            if request.method == 'GET' and response.status_code == 200:
                # GET请求缓存5分钟
                response.headers['Cache-Control'] = 'public, max-age=300'
            else:
                # 其他请求不缓存
                response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
                response.headers['Pragma'] = 'no-cache'
                response.headers['Expires'] = '0'
        
        return response

def add_security_headers(app: Flask):
    """添加安全头"""
    
    @app.after_request
    def set_security_headers(response):
        # 安全头
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # CSP头（内容安全策略）
        csp = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; "
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
            "font-src 'self' https://cdn.jsdelivr.net; "
            "img-src 'self' data: https:; "
            "connect-src 'self';"
        )
        response.headers['Content-Security-Policy'] = csp
        
        return response

def optimize_static_serving(app: Flask):
    """优化静态文件服务"""
    
    @app.route('/static/<path:filename>')
    def optimized_static(filename):
        """优化的静态文件服务"""
        try:
            # 检查文件是否存在
            static_folder = app.static_folder or 'static'
            filepath = os.path.join(static_folder, filename)
            
            if not os.path.exists(filepath):
                return "File not found", 404
            
            # 检查If-None-Match头（ETag）
            file_etag = f'"{os.path.getmtime(filepath)}"'
            if request.headers.get('If-None-Match') == file_etag:
                return "", 304
            
            # 获取MIME类型
            mimetype = mimetypes.guess_type(filename)[0] or 'application/octet-stream'
            
            # 创建响应
            response = make_response(send_file(filepath, mimetype=mimetype))
            
            # 设置缓存头
            response.headers['ETag'] = file_etag
            response.headers['Cache-Control'] = 'public, max-age=31536000'
            
            # 如果支持gzip且文件类型适合压缩
            if (request.headers.get('Accept-Encoding', '').find('gzip') != -1 and
                mimetype in {'text/css', 'text/javascript', 'application/javascript'}):
                
                # 检查是否有压缩版本
                gzip_path = filepath + '.gz'
                if os.path.exists(gzip_path) and os.path.getmtime(gzip_path) >= os.path.getmtime(filepath):
                    response = make_response(send_file(gzip_path, mimetype=mimetype))
                    response.headers['Content-Encoding'] = 'gzip'
                    response.headers['Vary'] = 'Accept-Encoding'
            
            return response
            
        except Exception as e:
            logger.error(f"服务静态文件失败 {filename}: {e}")
            return "Internal Server Error", 500

def init_static_optimization(app: Flask):
    """初始化静态资源优化"""
    # 启用压缩
    enable_compression(app)
    
    # 添加缓存头
    add_cache_headers(app)
    
    # 添加安全头
    add_security_headers(app)
    
    # 优化静态文件服务
    optimize_static_serving(app)
    
    logger.info("静态资源优化已启用")

# 预压缩静态文件的工具函数
def precompress_static_files(static_dir='static'):
    """预压缩静态文件"""
    if not os.path.exists(static_dir):
        return
    
    for root, dirs, files in os.walk(static_dir):
        for file in files:
            if file.endswith(('.css', '.js', '.html', '.json', '.xml', '.txt')):
                filepath = os.path.join(root, file)
                gzip_path = filepath + '.gz'
                
                # 如果压缩文件不存在或源文件更新
                if (not os.path.exists(gzip_path) or 
                    os.path.getmtime(filepath) > os.path.getmtime(gzip_path)):
                    
                    try:
                        with open(filepath, 'rb') as f_in:
                            with gzip.open(gzip_path, 'wb') as f_out:
                                f_out.write(f_in.read())
                        logger.info(f"预压缩文件: {filepath}")
                    except Exception as e:
                        logger.error(f"预压缩失败 {filepath}: {e}")

if __name__ == '__main__':
    # 预压缩静态文件
    precompress_static_files()
