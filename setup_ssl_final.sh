#!/bin/bash

# SSL配置脚本 for pd.beimoyinhenlinlin.cn
# 使用Let's Encrypt自动配置SSL证书

set -e

DOMAIN="pd.beimoyinhenlinlin.cn"
EMAIL="<EMAIL>"  # 请替换为您的邮箱
WEBROOT="/var/www/hospital-appointment"

echo "开始为域名 $DOMAIN 配置SSL证书..."

# 1. 安装certbot
echo "1. 安装certbot..."
apt-get update
apt-get install -y certbot python3-certbot-nginx

# 2. 创建基础nginx配置
echo "2. 创建基础nginx配置..."
cat > /etc/nginx/sites-available/hospital-appointment << 'EOF'
server {
    listen 80;
    server_name pd.beimoyinhenlinlin.cn;
    
    # Let's Encrypt验证路径
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    # 其他请求转发到应用
    location / {
        proxy_pass http://127.0.0.1:5010;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 8k;
        proxy_buffers 8 8k;
    }
    
    # 静态文件缓存
    location /static/ {
        alias /var/www/hospital-appointment/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        gzip on;
        gzip_types text/css application/javascript text/javascript;
    }
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
}
EOF

# 3. 启用站点
echo "3. 启用nginx站点..."
ln -sf /etc/nginx/sites-available/hospital-appointment /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx

# 4. 获取SSL证书
echo "4. 获取SSL证书..."
certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email $EMAIL --redirect

# 5. 创建优化的SSL配置
echo "5. 优化SSL配置..."
cat > /etc/nginx/sites-available/hospital-appointment << 'EOF'
server {
    listen 80;
    server_name pd.beimoyinhenlinlin.cn;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name pd.beimoyinhenlinlin.cn;
    
    # SSL配置
    ssl_certificate /etc/letsencrypt/live/pd.beimoyinhenlinlin.cn/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/pd.beimoyinhenlinlin.cn/privkey.pem;
    
    # SSL优化
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/json
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 主要应用代理
    location / {
        proxy_pass http://127.0.0.1:5010;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 8k;
        proxy_buffers 8 8k;
    }
    
    # 静态文件优化
    location /static/ {
        alias /var/www/hospital-appointment/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # 预压缩文件支持
        location ~* \.(css|js)$ {
            gzip_static on;
        }
    }
    
    # API缓存
    location ~* ^/(status|health|performance-stats)$ {
        proxy_pass http://127.0.0.1:5010;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 短期缓存
        expires 5m;
        add_header Cache-Control "public, max-age=300";
    }
    
    # 健康检查
    location = /nginx-health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

# 6. 重新加载nginx
echo "6. 重新加载nginx配置..."
nginx -t
systemctl reload nginx

# 7. 设置自动续期
echo "7. 设置SSL证书自动续期..."
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -

# 8. 测试SSL配置
echo "8. 测试SSL配置..."
sleep 5
curl -I https://$DOMAIN/ || echo "SSL测试可能需要DNS传播时间"

echo "✅ SSL配置完成！"
echo "🔒 您的网站现在可以通过 https://$DOMAIN 安全访问"
echo "📱 证书将自动续期"
echo ""
echo "测试链接："
echo "- https://$DOMAIN/"
echo "- https://$DOMAIN/admin"
echo "- https://$DOMAIN/tcm-admin"
echo "- https://$DOMAIN/status"
