#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库状态迁移脚本
将盆底康复表中的状态从"已到"更新为"已就诊"，确保后台管理和每日病人管理状态一致
"""

from db_connection import Database
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_status():
    """迁移状态从"已到"到"已就诊" """
    try:
        with Database() as db:
            # 检查当前有多少条"已到"状态的记录
            check_query = "SELECT COUNT(*) FROM pelvic_rehab WHERE status = '已到'"
            db.cursor.execute(check_query)
            count = db.cursor.fetchone()[0]
            
            if count > 0:
                logger.info(f"发现 {count} 条需要更新的记录（状态：已到 -> 已就诊）")
                
                # 更新状态
                update_query = "UPDATE pelvic_rehab SET status = '已就诊' WHERE status = '已到'"
                db.cursor.execute(update_query)
                db.connection.commit()
                
                logger.info(f"成功更新了 {count} 条记录的状态")
            else:
                logger.info("没有发现需要更新的记录")
                
            # 验证更新结果
            verify_query = """
                SELECT status, COUNT(*) as count 
                FROM pelvic_rehab 
                WHERE status IS NOT NULL 
                GROUP BY status
            """
            db.cursor.execute(verify_query)
            results = db.cursor.fetchall()
            
            logger.info("当前盆底康复预约状态分布：")
            for status, count in results:
                logger.info(f"  {status}: {count} 条")
                
    except Exception as e:
        logger.error(f"状态迁移失败: {e}")
        raise

if __name__ == "__main__":
    logger.info("开始状态迁移...")
    migrate_status()
    logger.info("状态迁移完成！")