from flask import Flask, request, g
from routes.rehab import rehab_bp
from routes.admin import admin_bp
from routes.tcm import tcm_bp
from routes.tcm_admin import tcm_admin_bp
from static_optimizer import init_static_optimization
import time
import logging
import atexit

# 尝试导入性能监控，如果失败则跳过
try:
    from performance_monitor import monitor
    PERFORMANCE_MONITORING = True
except ImportError:
    PERFORMANCE_MONITORING = False
    print("性能监控模块未找到，跳过性能监控功能")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

app = Flask(__name__)

# Flask性能优化配置
app.config.update(
    # 禁用调试模式
    DEBUG=False,
    # 启用JSON排序
    JSON_SORT_KEYS=False,
    # 设置最大内容长度
    MAX_CONTENT_LENGTH=16 * 1024 * 1024,  # 16MB
    # 启用压缩
    COMPRESS_MIMETYPES=[
        'text/html', 'text/css', 'text/xml', 'application/json',
        'application/javascript', 'text/javascript'
    ]
)

# 初始化静态资源优化
init_static_optimization(app)

# 性能监控中间件
@app.before_request
def before_request():
    g.start_time = time.time()

@app.after_request
def after_request(response):
    # 添加基本的响应头
    if hasattr(g, 'start_time'):
        duration = time.time() - g.start_time
        response.headers['X-Response-Time'] = f"{duration:.3f}s"

        # 如果性能监控可用，记录请求时间
        if PERFORMANCE_MONITORING:
            monitor.record_request_time(request.endpoint or 'unknown', duration)

    return response

# 状态和性能监控路由
@app.route('/status')
def status():
    """获取应用状态"""
    from db_pool import get_db_pool
    from cache_manager import get_cache_manager
    
    db_pool = get_db_pool()
    cache_manager = get_cache_manager()
    
    return {
        'status': 'ok',
        'timestamp': time.time(),
        'message': '应用程序正常运行',
        'performance_monitoring': PERFORMANCE_MONITORING,
        'database_pool': db_pool.get_stats(),
        'cache': cache_manager.get_stats()
    }

@app.route('/performance-stats')
def performance_stats():
    """获取性能统计信息"""
    if not PERFORMANCE_MONITORING:
        return {'error': '性能监控未启用'}, 503

    stats = monitor.get_stats()
    return {
        'performance': stats,
        'timestamp': time.time()
    }

# 测试路由
@app.route('/test')
def test():
    """测试路由"""
    return {'status': 'ok', 'message': '应用程序正常运行'}

# 健康检查路由
@app.route('/health')
def health_check():
    """健康检查"""
    try:
        from db_connection_optimized import OptimizedDatabase
        
        # 测试数据库连接
        with OptimizedDatabase() as db:
            # 简单的数据库查询测试
            db._execute_query("SELECT 1", fetch_one=True)
        
        return {
            'status': 'healthy',
            'timestamp': time.time(),
            'database': 'connected'
        }
    except Exception as e:
        return {
            'status': 'unhealthy',
            'timestamp': time.time(),
            'error': str(e)
        }, 500

# 注册蓝图
app.register_blueprint(rehab_bp)
app.register_blueprint(admin_bp)
app.register_blueprint(tcm_bp)
app.register_blueprint(tcm_admin_bp)

# 应用关闭时的清理工作
def cleanup():
    """应用关闭时的清理工作"""
    try:
        from db_pool import close_db_pool
        close_db_pool()
        logging.info("数据库连接池已关闭")
    except Exception as e:
        logging.error(f"清理资源时出错: {e}")

# 注册清理函数
atexit.register(cleanup)

if __name__ == '__main__':
    # 生产环境配置
    app.run(host='0.0.0.0', port=5010, debug=False)
