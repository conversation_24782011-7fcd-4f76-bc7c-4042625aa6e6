<!DOCTYPE html>
<html>
<head>
    <title>涟源市妇幼保健院盆底康复预约系统</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            width: 90%;
            max-width: 600px;
            margin: 20px;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #2196F3, #4CAF50);
            margin: -30px -30px 30px -30px;
            border-radius: 15px 15px 0 0;
        }

        .header h2 {
            color: white;
            margin: 0;
            font-size: 24px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        input, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }

        input:focus, select:focus {
            border-color: #2196F3;
            outline: none;
        }

        button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #2196F3, #4CAF50);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            cursor: pointer;
            margin-top: 20px;
        }

        button:hover {
            opacity: 0.9;
        }

        .notice {
            margin-top: 20px;
            padding: 15px;
            background: #fff3e0;
            border-radius: 8px;
            font-size: 14px;
            color: #f57c00;
        }

        .back-link {
            display: block;
            text-align: center;
            margin-top: 20px;
            color: #2196F3;
            text-decoration: none;
        }

        .query-section {
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .query-title {
            text-align: center;
            color: #333;
            font-size: 18px;
            margin: 0 0 15px 0;
            font-weight: 500;
        }

        .query-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: stretch;
        }

        .query-form .form-group {
            margin: 0;
        }

        .query-form input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .query-form input:focus {
            border-color: #2196F3;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .query-button {
            padding: 12px 25px;
            background: linear-gradient(45deg, #2196F3, #4CAF50);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .query-button:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        .query-button:active {
            transform: translateY(1px);
        }

        /* 移动端适配 */
        @media (max-width: 480px) {
            .query-section {
                padding: 15px;
                margin-top: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>涟源市妇幼保健院盆底康复预约</h2>
        </div>

        <form action="/submit-rehab" method="post" onsubmit="return validateForm()">
            <div class="form-group">
                <label for="name">姓名</label>
                <input type="text" id="name" name="name" required placeholder="请输入真实姓名">
            </div>

            <div class="form-group">
                <label for="phone">手机号码</label>
                <input type="tel" id="phone" name="phone" required placeholder="请输入11位手机号">
            </div>

            <div class="form-group">
                <label for="date">预约日期</label>
                <input type="date" id="date" name="date" required>
            </div>

            <div class="form-group">
                <label for="time_slot">就诊时段</label>
                <select id="time_slot" name="time_slot" required>
                    <option value="">请选择就诊时段</option>
                    {% for slot in time_slots %}
                        <option value="{{ slot.label }}">{{ slot.label }}（限{{ slot.max_count }}人）</option>
                    {% endfor %}
                </select>
            </div>

            <button type="submit">提交预约</button>
        </form>

        <div class="query-section">
            <h3 class="query-title">查询预约记录</h3>
            <form action="/query-rehab" method="post" class="query-form">
                <div class="form-group">
                    <input type="tel" id="query_phone" name="query_phone" required 
                           placeholder="请输入手机号查询" pattern="1\d{10}"
                           title="请输��11位手机号码">
                </div>
                <button type="submit" class="query-button">查询</button>
            </form>
        </div>

        <div class="notice">
            温馨提示：<br>
            1. 每个时段限约8人<br>
            2. 请选择合适的时间段预约<br>
            3. 同一手机号重复预约将更新原有预约信息<br>
        </div>

        <a href="/" class="back-link">返回首页</a>
    </div>

    <script>
        // 保存表单数据到 localStorage
        function saveFormData() {
            var name = document.getElementById('name').value;
            var phone = document.getElementById('phone').value;
            
            localStorage.setItem('rehab_name', name);
            localStorage.setItem('rehab_phone', phone);
        }

        // 从 localStorage 加载表单数据
        function loadFormData() {
            var name = localStorage.getItem('rehab_name');
            var phone = localStorage.getItem('rehab_phone');
            
            if (name) document.getElementById('name').value = name;
            if (phone) document.getElementById('phone').value = phone;
        }

        // 解析时间段结束时间
        function parseTimeSlotEndTime(timeSlot) {
            try {
                // 移除中文前缀
                var timePart = timeSlot.replace(/^(上午|下午|晚上)/, '');

                // 查找时间范围分隔符
                if (timePart.includes('-')) {
                    var parts = timePart.split('-');
                    if (parts.length >= 2) {
                        var endTimeStr = parts[1].trim();
                        // 解析时间 HH:MM 或 H:MM
                        var timeMatch = endTimeStr.match(/(\d{1,2}):(\d{2})/);
                        if (timeMatch) {
                            var hour = parseInt(timeMatch[1]);
                            var minute = parseInt(timeMatch[2]);
                            return { hour: hour, minute: minute };
                        }
                    }
                }
            } catch (e) {
                console.log('解析时间段失败:', e);
            }
            return null;
        }

        // 检查时间段是否已过期
        function isTimeSlotExpired(selectedDate, timeSlot) {
            var today = new Date();
            var appointmentDate = new Date(selectedDate);

            // 如果不是今天，不需要检查时间
            if (appointmentDate.toDateString() !== today.toDateString()) {
                return false;
            }

            // 解析时间段结束时间
            var endTime = parseTimeSlotEndTime(timeSlot);
            if (!endTime) {
                return false; // 无法解析时间，不限制
            }

            // 创建今天的结束时间
            var endDateTime = new Date();
            endDateTime.setHours(endTime.hour, endTime.minute, 0, 0);

            // 检查当前时间是否已经超过结束时间
            return today >= endDateTime;
        }

        // 表单验证并保存数据
        function validateForm() {
            var phone = document.getElementById('phone').value;
            var selectedDate = document.getElementById('date').value;
            var timeSlot = document.getElementById('time_slot').value;

            if (!/^1\d{10}$/.test(phone)) {
                alert('请输入正确的11位手机号码');
                return false;
            }

            if (!selectedDate) {
                alert('请选择预约日期');
                return false;
            }

            if (!timeSlot) {
                alert('请选择就诊时段');
                return false;
            }

            // 验证日期不能是过去的日期
            var today = new Date();
            var appointmentDate = new Date(selectedDate);
            today.setHours(0, 0, 0, 0);
            appointmentDate.setHours(0, 0, 0, 0);

            if (appointmentDate < today) {
                alert('不能预约过去的日期，请选择今天或未来的日期');
                return false;
            }

            // 如果是今天，检查时间段是否已过期
            if (isTimeSlotExpired(selectedDate, timeSlot)) {
                alert('所选时间段已经过去，请选择其他时间段');
                return false;
            }

            // 验证通过后保存数据
            saveFormData();
            return true;
        }

        // 更新时间段选项的可用性
        function updateTimeSlotAvailability() {
            var selectedDate = document.getElementById('date').value;
            var timeSlotSelect = document.getElementById('time_slot');
            var options = timeSlotSelect.options;

            for (var i = 1; i < options.length; i++) { // 跳过第一个空选项
                var option = options[i];
                var timeSlot = option.value;

                if (selectedDate && isTimeSlotExpired(selectedDate, timeSlot)) {
                    option.disabled = true;
                    option.style.color = '#ccc';
                    option.text = option.text.replace('（已过期）', '') + '（已过期）';
                } else {
                    option.disabled = false;
                    option.style.color = '';
                    option.text = option.text.replace('（已过期）', '');
                }
            }

            // 如果当前选中的时间段已过期，清空选择
            if (timeSlotSelect.value && selectedDate && isTimeSlotExpired(selectedDate, timeSlotSelect.value)) {
                timeSlotSelect.value = '';
                alert('所选时间段已经过去，请重新选择');
            }
        }

        // 页面加载时加载数据
        window.onload = function() {
            loadFormData();

            // 设置日期选择范围
            var dateInput = document.getElementById('date');
            var timeSlotSelect = document.getElementById('time_slot');
            var today = new Date();
            var maxDate = new Date();
            maxDate.setMonth(maxDate.getMonth() + 1);  // 只能预约一个月内

            // 设置最小日期为今天
            dateInput.min = today.toISOString().split('T')[0];
            dateInput.max = maxDate.toISOString().split('T')[0];

            // 添加事件监听器
            dateInput.addEventListener('change', updateTimeSlotAvailability);
            timeSlotSelect.addEventListener('change', function() {
                var selectedDate = dateInput.value;
                var timeSlot = this.value;

                if (selectedDate && timeSlot && isTimeSlotExpired(selectedDate, timeSlot)) {
                    alert('所选时间段已经过去，请选择其他时间段');
                    this.value = '';
                }
            });

            // 初始化时间段可用性
            updateTimeSlotAvailability();
        };
    </script>
</body>
</html>