<!DOCTYPE html>
<html>
<head>
    <title>涟源市妇幼保健院预约系统</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <style>
        * {
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            margin: 0;
            padding: 10px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: url('https://api.ixiaowai.cn/gqapi/gqapi.php') center/cover no-repeat fixed;
            position: relative;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.92);
            z-index: 1;
        }

        .container {
            width: 95%;
            max-width: 600px;
            margin: 10px auto;
            padding: 20px 15px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            position: relative;
            z-index: 2;
            backdrop-filter: blur(10px);
            box-sizing: border-box;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 25px;
            background: linear-gradient(135deg, #1a73e8, #34a853);
            margin: -30px -30px 30px -30px;
            border-radius: 15px 15px 0 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .header h2 {
            color: white;
            margin: 0;
            font-size: 26px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .menu-item {
            display: block;
            width: 100%;
            max-width: 400px;
            padding: 20px 15px;
            margin: 12px auto;
            background: white;
            border: none;
            border-radius: 16px;
            text-align: center;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            box-sizing: border-box;
            box-shadow:
                0 4px 15px rgba(0,0,0,0.1),
                inset 0 -3px 0 rgba(0,0,0,0.1),
                inset 0 2px 0 rgba(255,255,255,0.5);
            cursor: pointer;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }

        .menu-item.rehab {
            color: #2196F3;
            background: linear-gradient(145deg, #ffffff, #f5f9ff);
        }

        .menu-item.tcm {
            color: #FF6B35;
            background: linear-gradient(145deg, #ffffff, #fff8f5);
        }

        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a73e8, #34a853);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .menu-item:hover {
            color: white !important;
            transform: translateY(-3px) scale(1.02);
            box-shadow: 
                0 8px 25px rgba(0,0,0,0.15),
                inset 0 -3px 0 rgba(0,0,0,0.2),
                inset 0 2px 0 rgba(255,255,255,0.5);
        }

        .menu-item:active {
            transform: translateY(1px);
            box-shadow: 
                0 2px 10px rgba(0,0,0,0.1),
                inset 0 -1px 0 rgba(0,0,0,0.1);
        }

        .menu-item .icon {
            font-size: 28px;
            margin-right: 12px;
            vertical-align: middle;
            filter: drop-shadow(0 2px 3px rgba(0,0,0,0.2));
        }

        .hospital-info {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }

        .hospital-info p {
            margin: 5px 0;
        }

        .notice {
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            margin-bottom: 20px;
            color: #666;
            font-size: 15px;
            line-height: 1.8;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .notice strong {
            color: #1a73e8;
            display: block;
            margin-bottom: 10px;
            font-size: 16px;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            body {
                padding: 5px;
            }

            .container {
                width: 98%;
                margin: 5px auto;
                padding: 15px 10px;
                border-radius: 12px;
            }

            .header {
                margin: -15px -10px 15px -10px;
                padding: 15px 10px;
                border-radius: 12px 12px 0 0;
            }

            .header h2 {
                font-size: 18px;
                line-height: 1.3;
            }

            .menu-item {
                width: 95%;
                max-width: none;
                padding: 16px 12px;
                margin: 10px auto;
                font-size: 15px;
                border-radius: 12px;
                min-height: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
            }

            .icon {
                font-size: 22px;
                margin-bottom: 4px;
                display: block;
            }

            .notice {
                margin: 0 auto 15px auto;
                width: 95%;
                padding: 12px;
                font-size: 13px;
                line-height: 1.4;
                border-radius: 8px;
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 360px) {
            .container {
                width: 100%;
                margin: 0;
                padding: 10px 8px;
                border-radius: 0;
            }

            .header {
                margin: -10px -8px 10px -8px;
                padding: 12px 8px;
                border-radius: 0;
            }

            .header h2 {
                font-size: 16px;
            }

            .menu-item {
                width: 100%;
                padding: 14px 10px;
                margin: 8px auto;
                font-size: 14px;
                min-height: 55px;
            }

            .icon {
                font-size: 20px;
            }

            .notice {
                font-size: 12px;
                padding: 10px;
            }
        }

        /* 触摸设备优化 */
        @media (hover: none) and (pointer: coarse) {
            .menu-item:active {
                transform: scale(0.98);
                box-shadow:
                    0 2px 8px rgba(0,0,0,0.15),
                    inset 0 -2px 0 rgba(0,0,0,0.15),
                    inset 0 1px 0 rgba(255,255,255,0.3);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>涟源市妇幼保健院预约系统</h2>
        </div>

        <div class="notice">
            <strong>温馨提示</strong>
            为方便大家就诊，减少等待时间，请提前预约<br>
            1. 盆底康复按时段预约：<br>
               上午：08-09点、09-10点、10-11点、11-12点<br>
               下午：14-15点、15-16点、16-17点<br>
               每个时段限约5人<br>
            2. 中医特色治疗按时段预约：<br>
               上午：08-09点、09-10点、10-11点、11-12点<br>
               下午：14:30-15:30点、15:30-16:30点、16:30-17:30点<br>
               每个时段限约3人<br>
               如有疑问，请拨打4428713<br>
            请确保信息真实有效，重复预约将更新原有预约信息
        </div>

        <a href="/pelvic-rehab" class="menu-item rehab">
            <span class="icon">👨‍⚕️</span>
            盆底康复预约
        </a>

        <a href="/tcm-treatment" class="menu-item tcm">
            <span class="icon">🌿</span>
            中医特色治疗预约
        </a>

        <div class="hospital-info">
            <p>涟源市妇幼保健院</p>
            <p>地址：湖南省涟源市交通路三角坪</p>
            <p>咨询电话：0738-4428716</p>
        </div>
    </div>
</body>
</html>