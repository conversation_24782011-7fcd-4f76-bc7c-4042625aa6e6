<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>每日中医特色治疗预约病人管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            width: 95%;
            max-width: 900px;
            margin: 30px auto;
            padding: 35px 25px;
            background: white;
            border-radius: 18px;
            box-shadow: 0 6px 32px rgba(0,0,0,0.10);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #FF6B35, #F7931E);
            margin: -35px -25px 30px -25px;
            border-radius: 18px 18px 0 0;
            color: white;
        }
        .header h2 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .table th {
            background: #f8f9fa;
            border-top: none;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }
        .table td {
            vertical-align: middle;
            font-size: 14px;
        }
        .badge {
            font-size: 12px;
            padding: 6px 10px;
        }
        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .status-btn {
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin: 1px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .status-btn.active {
            opacity: 0.8;
        }
        .status-btn:hover {
            opacity: 0.9;
        }
        .status-未到 { background: #6c757d; color: white; }
        .status-已就诊 { background: #28a745; color: white; }
        .status-已取消 { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>每日中医特色治疗预约病人管理</h2>
        </div>
        <form class="row g-3 mb-3" method="get" action="">
            <div class="col-auto">
                <input type="date" class="form-control" name="date" value="{{ query_date }}">
            </div>
            <div class="col-auto">
                <button type="submit" class="btn btn-primary">查询</button>
            </div>
            <div class="col-auto">
                <a href="/tcm-admin" class="btn btn-secondary">返回管理首页</a>
            </div>
        </form>
        <div class="table-responsive">
            <table class="table table-bordered table-hover align-middle" id="patients-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>姓名</th>
                        <th>手机号</th>
                        <th>日期</th>
                        <th>时间段</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                {% for appt in appointments %}
                    <tr data-id="{{ appt.id }}">
                        <td>{{ appt.id }}</td>
                        <td>{{ appt.name }}</td>
                        <td>{{ appt.phone }}</td>
                        <td>{{ appt.date }}</td>
                        <td>{{ appt.time_slot }}</td>
                        <td>
                            <span class="badge 
                                {% if appt.status == '未到' %}bg-secondary
                                {% elif appt.status == '已就诊' %}bg-success
                                {% elif appt.status == '已取消' %}bg-danger
                                {% endif %}">
                                {{ appt.status }}
                            </span>
                        </td>
                        <td>
                            <button class="status-btn status-未到" onclick="updateStatus({{ appt.id }}, '未到')">未到</button>
                            <button class="status-btn status-已就诊" onclick="updateStatus({{ appt.id }}, '已就诊')">已就诊</button>
                            <button class="status-btn status-已取消" onclick="updateStatus({{ appt.id }}, '已取消')">已取消</button>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateStatus(appointmentId, newStatus) {
            fetch('/tcm-admin/update-patient-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `appointment_id=${appointmentId}&status=${newStatus}`
            })
            .then(response => {
                if (response.ok) {
                    // 更新页面上的状态显示
                    const row = document.querySelector(`tr[data-id="${appointmentId}"]`);
                    const statusBadge = row.querySelector('.badge');
                    
                    // 更新状态文本
                    statusBadge.textContent = newStatus;
                    
                    // 更新状态样式
                    statusBadge.className = 'badge ';
                    if (newStatus === '未到') {
                        statusBadge.className += 'bg-secondary';
                    } else if (newStatus === '已就诊') {
                        statusBadge.className += 'bg-success';
                    } else if (newStatus === '已取消') {
                        statusBadge.className += 'bg-danger';
                    }
                } else {
                    alert('更新状态失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('更新状态失败，请重试');
            });
        }
    </script>
</body>
</html>
