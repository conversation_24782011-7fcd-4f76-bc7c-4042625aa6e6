<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>每日预约病人管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            width: 95%;
            max-width: 900px;
            margin: 30px auto;
            padding: 35px 25px;
            background: white;
            border-radius: 18px;
            box-shadow: 0 6px 32px rgba(0,0,0,0.10);
        }
        .header {
            text-align: center;
            margin-bottom: 35px;
            padding: 22px;
            background: linear-gradient(45deg, #2196F3, #4CAF50);
            margin: -35px -25px 35px -25px;
            border-radius: 18px 18px 0 0;
        }
        .header h2 {
            color: white;
            margin: 0;
            font-size: 28px;
            letter-spacing: 2px;
        }
        table {
            background: #fafbfc;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(33,150,243,0.04);
        }
        th, td {
            text-align: center;
            vertical-align: middle !important;
        }
        thead th {
            background: linear-gradient(45deg, #2196F3, #4CAF50);
            color: #fff;
            border: none;
        }
        tbody tr {
            transition: background 0.2s;
        }
        tbody tr:hover {
            background: #e3f2fd;
        }
        .btn-status {
            min-width: 80px;
        }
        @media (max-width: 700px) {
            .container {
                padding: 10px 2px;
            }
            .header {
                padding: 12px;
                font-size: 20px;
            }
            table, thead, tbody, th, td, tr {
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>每日预约病人管理</h2>
        </div>
        <form class="row g-3 mb-3" method="get" action="">
            <div class="col-auto">
                <input type="date" class="form-control" name="date" value="{{ query_date }}">
            </div>
            <div class="col-auto">
                <button type="submit" class="btn btn-primary">查询</button>
            </div>
            <div class="col-auto">
                <a href="/admin" class="btn btn-secondary">返回管理首页</a>
            </div>
        </form>
        <div class="table-responsive">
            <table class="table table-bordered table-hover align-middle" id="patients-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>姓名</th>
                        <th>手机号</th>
                        <th>日期</th>
                        <th>时间段</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                {% for appt in appointments %}
                    <tr data-id="{{ appt.id }}">
                        <td>{{ appt.id }}</td>
                        <td>{{ appt.name }}</td>
                        <td>{{ appt.phone }}</td>
                        <td>{{ appt.date }}</td>
                        <td>{{ appt.time_slot }}</td>
                        <td class="status-cell">
                            <span class="badge {% if appt.status == '未到' %}bg-secondary{% elif appt.status == '已就诊' %}bg-success{% elif appt.status == '已取消' %}bg-danger{% endif %}">{{ appt.status }}</span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-secondary" data-id="{{ appt.id }}" data-status="未到" onclick="updateStatus(this.dataset.id, this.dataset.status)">未到</button>
                            <button class="btn btn-sm btn-outline-success" data-id="{{ appt.id }}" data-status="已就诊" onclick="updateStatus(this.dataset.id, this.dataset.status)">已就诊</button>
                            <button class="btn btn-sm btn-outline-danger" data-id="{{ appt.id }}" data-status="已取消" onclick="updateStatus(this.dataset.id, this.dataset.status)">已取消</button>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    <script>
    function updateStatus(appointmentId, newStatus) {
        fetch('/admin/update-patient-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `appointment_id=${appointmentId}&status=${newStatus}`
        })
        .then(response => {
            if (response.ok) {
                // 更新页面上的状态显示
                const row = document.querySelector(`tr[data-id="${appointmentId}"]`);
                const statusBadge = row.querySelector('.badge');
                
                // 更新状态文本
                statusBadge.textContent = newStatus;
                
                // 更新状态样式
                statusBadge.className = 'badge ';
                if (newStatus === '未到') {
                    statusBadge.className += 'bg-secondary';
                } else if (newStatus === '已就诊') {
                    statusBadge.className += 'bg-success';
                } else if (newStatus === '已取消') {
                    statusBadge.className += 'bg-danger';
                }
            } else {
                alert('更新状态失败，请重试');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('更新状态失败，请重试');
        });
    }
    </script>
</body>
</html> 