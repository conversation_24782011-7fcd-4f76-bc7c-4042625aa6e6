<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>患者查询 - {% if treatment_type == 'rehab' %}盆底康复{% else %}中医特色治疗{% endif %}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        :root {
            {% if treatment_type == 'rehab' %}
            --primary-color: #4f46e5;
            --primary-light: #818cf8;
            --background-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            {% else %}
            --primary-color: #f97316;
            --primary-light: #fb923c;
            --background-gradient: linear-gradient(135deg, #fefcfb 0%, #fef4ed 100%);
            {% endif %}
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            --border-radius: 16px;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--background-gradient);
            min-height: 100vh;
            padding: 20px 0;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--card-shadow);
            text-align: center;
            color: white;
        }

        .page-header h1 {
            margin: 0;
            font-size: 1.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .search-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .search-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            {% if treatment_type == 'rehab' %}
            background: #f8fafc;
            {% else %}
            background: #fefcfb;
            {% endif %}
        }

        .search-body {
            padding: 1.5rem;
        }

        .search-form {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .search-input {
            flex: 1;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: border-color 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            border: none;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            {% if treatment_type == 'rehab' %}
            background: #3730a3;
            {% else %}
            background: #ea580c;
            {% endif %}
            transform: translateY(-1px);
        }

        .btn-outline-secondary {
            border: 2px solid #6b7280;
            color: #6b7280;
            background: transparent;
        }

        .btn-outline-secondary:hover {
            background: #6b7280;
            color: white;
            transform: translateY(-1px);
        }

        .results-container {
            display: grid;
            gap: 2rem;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.25rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.05);
            text-align: center;
        }

        .stat-value {
            font-size: 1.75rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #64748b;
            margin: 0.25rem 0 0 0;
        }

        .content-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            {% if treatment_type == 'rehab' %}
            background: #f8fafc;
            {% else %}
            background: #fefcfb;
            {% endif %}
        }

        .card-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .table-container {
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }

        .table {
            margin: 0;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            {% if treatment_type == 'rehab' %}
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            {% else %}
            background: linear-gradient(135deg, #fefcfb 0%, #fef4ed 100%);
            {% endif %}
            border: none;
            padding: 1rem;
            font-weight: 600;
            color: #475569;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .table tbody td {
            padding: 1rem;
            border-top: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            {% if treatment_type == 'rehab' %}
            background: #f8fafc;
            {% else %}
            background: #fefcfb;
            {% endif %}
        }

        .status-badge {
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .status-badge.completed {
            background: #dcfce7;
            color: #166534;
        }

        .status-badge.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-badge.cancelled {
            background: #fee2e2;
            color: #991b1b;
        }

        .no-results {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
        }

        .no-results i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 0 15px;
            }

            .search-form {
                flex-direction: column;
            }

            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .table-container {
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1>
                <i class="bi bi-search"></i>
                患者查询 - {% if treatment_type == 'rehab' %}盆底康复{% else %}中医特色治疗{% endif %}
            </h1>
        </div>

        <!-- 搜索表单 -->
        <div class="search-card">
            <div class="search-header">
                <h3>
                    <i class="bi bi-person-search"></i>
                    查找患者信息
                </h3>
            </div>
            <div class="search-body">
                <form method="post" class="search-form">
                    <input type="text" name="keyword" class="search-input" 
                           placeholder="输入患者姓名或手机号码进行搜索..." 
                           value="{{ keyword }}" required>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i>
                        搜索
                    </button>
                    {% if treatment_type == 'rehab' %}
                    <a href="/admin" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        返回管理
                    </a>
                    {% else %}
                    <a href="/tcm-admin" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        返回管理
                    </a>
                    {% endif %}
                </form>
            </div>
        </div>

        <!-- 搜索结果 -->
        {% if patient_info is not none %}
            {% if patient_info.stats %}
                <!-- 患者统计信息 -->
                {% for stat in patient_info.stats %}
                <div class="stats-overview">
                    <div class="stat-card">
                        <p class="stat-value">{{ stat.total_count }}</p>
                        <p class="stat-label">总预约次数</p>
                    </div>
                    <div class="stat-card">
                        <p class="stat-value">{{ stat.completed_count }}</p>
                        <p class="stat-label">已完成</p>
                    </div>
                    <div class="stat-card">
                        <p class="stat-value">{{ stat.pending_count }}</p>
                        <p class="stat-label">待就诊</p>
                    </div>
                    <div class="stat-card">
                        <p class="stat-value">{{ stat.cancelled_count }}</p>
                        <p class="stat-label">已取消</p>
                    </div>
                    {% if treatment_type == 'rehab' and pelvic_course_threshold %}
                    <div class="stat-card">
                        <p class="stat-value">
                            {% if stat.completed_count >= pelvic_course_threshold %}
                                <i class="bi bi-check-circle text-success"></i>
                            {% else %}
                                <i class="bi bi-clock text-warning"></i>
                            {% endif %}
                        </p>
                        <p class="stat-label">疗程状态</p>
                    </div>
                    {% elif treatment_type == 'tcm' and tcm_course_threshold %}
                    <div class="stat-card">
                        <p class="stat-value">
                            {% if stat.completed_count >= tcm_course_threshold %}
                                <i class="bi bi-check-circle text-success"></i>
                            {% else %}
                                <i class="bi bi-clock text-warning"></i>
                            {% endif %}
                        </p>
                        <p class="stat-label">疗程状态</p>
                    </div>
                    {% endif %}
                </div>

                <!-- 患者基本信息 -->
                <div class="content-card">
                    <div class="card-header">
                        <h3>
                            <i class="bi bi-person-circle"></i>
                            患者基本信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>姓名:</strong> {{ stat.name }}
                            </div>
                            <div class="col-md-3">
                                <strong>电话:</strong> {{ stat.phone }}
                            </div>
                            <div class="col-md-3">
                                <strong>首次预约:</strong> {{ stat.first_appointment }}
                            </div>
                            <div class="col-md-3">
                                <strong>最近预约:</strong> {{ stat.last_appointment }}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}

                <!-- 预约历史 -->
                {% if patient_info.appointments %}
                <div class="content-card">
                    <div class="card-header">
                        <h3>
                            <i class="bi bi-calendar-event"></i>
                            预约历史记录
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>预约编号</th>
                                        <th>预约日期</th>
                                        <th>时间段</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                {% for appt in patient_info.appointments %}
                                    <tr>
                                        <td class="text-muted">#{{ appt.id }}</td>
                                        <td class="fw-semibold">{{ appt.date }}</td>
                                        <td>{{ appt.time_slot }}</td>
                                        <td>
                                            {% if appt.status == '已就诊' %}
                                                <span class="status-badge completed">{{ appt.status }}</span>
                                            {% elif appt.status == '未到' %}
                                                <span class="status-badge pending">{{ appt.status }}</span>
                                            {% else %}
                                                <span class="status-badge cancelled">{{ appt.status }}</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-muted">{{ appt.created_at }}</td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% endif %}
            {% else %}
                <!-- 无搜索结果 -->
                <div class="content-card">
                    <div class="card-body">
                        <div class="no-results">
                            <i class="bi bi-search"></i>
                            <h4>未找到相关患者信息</h4>
                            <p>没有找到与 "{{ keyword }}" 相关的患者记录</p>
                            <p class="text-muted">请检查姓名或手机号码是否正确，或尝试使用部分关键词搜索</p>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% elif keyword %}
            <!-- 搜索但无结果 -->
            <div class="content-card">
                <div class="card-body">
                    <div class="no-results">
                        <i class="bi bi-search"></i>
                        <h4>未找到相关患者信息</h4>
                        <p>没有找到与 "{{ keyword }}" 相关的患者记录</p>
                        <p class="text-muted">请检查姓名或手机号码是否正确，或尝试使用部分关键词搜索</p>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 