<!DOCTYPE html>
<html>
<head>
    <title>提示信息</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            width: 90%;
            max-width: 600px;
            margin: 20px;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            text-align: center;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #2196F3, #4CAF50);
            margin: -30px -30px 30px -30px;
            border-radius: 15px 15px 0 0;
        }

        .header h2 {
            color: white;
            margin: 0;
            font-size: 24px;
        }

        .message {
            font-size: 18px;
            color: #f57c00;
            margin: 20px 0;
        }

        .back-link {
            display: inline-block;
            padding: 10px 20px;
            background: linear-gradient(45deg, #2196F3, #4CAF50);
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
            transition: opacity 0.3s;
        }

        .back-link:hover {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>提示信息</h2>
        </div>
        <div class="message">
            {{ message }}
        </div>
        <a href="/" class="back-link">返回首页</a>
    </div>
</body>
</html> 