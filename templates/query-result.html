<!DOCTYPE html>
<html>
<head>
    <title>预约记录查询结果</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            width: 90%;
            max-width: 600px;
            margin: 20px;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #2196F3, #4CAF50);
            margin: -30px -30px 30px -30px;
            border-radius: 15px 15px 0 0;
        }

        .header h2 {
            color: white;
            margin: 0;
            font-size: 24px;
        }

        .appointment-card {
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .appointment-card p {
            margin: 5px 0;
            color: #333;
        }

        .back-link {
            display: block;
            text-align: center;
            margin-top: 20px;
            padding: 10px;
            background: linear-gradient(45deg, #2196F3, #4CAF50);
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }

        .back-link:hover {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>预约记录查询结果</h2>
        </div>

        {% for appointment in appointments %}
        <div class="appointment-card">
            <p>姓名：{{ appointment.name }}</p>
            <p>手机号：{{ appointment.phone }}</p>
            <p>单位名称：{{ appointment.company }}</p>
            <p>预约日期：{{ appointment.appointment_date }}</p>
            <p>预约时段：{{ appointment.time_slot }}</p>
            <p>预约编号：{{ appointment.daily_number }}</p>
            <p>创建日期：{{ appointment.created_at }}</p>
        </div>
        {% endfor %}

        <a href="/pelvic-rehab" class="back-link">返回预约页面</a>
    </div>
</body>
</html> 