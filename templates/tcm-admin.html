<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>中医特色治疗预约管理后台</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #f97316;
            --primary-light: #fb923c;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --background-gradient: linear-gradient(135deg, #fefcfb 0%, #fef4ed 100%);
            --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            --border-radius: 16px;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--background-gradient);
            min-height: 100vh;
            padding: 20px 0;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--card-shadow);
            text-align: center;
            color: white;
        }

        .page-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .stat-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-right: 1rem;
            color: white;
        }

        .stat-icon.primary { background: var(--primary-color); }
        .stat-icon.success { background: var(--success-color); }
        .stat-icon.warning { background: var(--warning-color); }
        .stat-icon.info { background: #0ea5e9; }

        .stat-title {
            font-size: 0.875rem;
            color: #64748b;
            margin: 0;
            font-weight: 500;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        .content-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            background: #fefcfb;
        }

        .card-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            border: none;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #ea580c;
            transform: translateY(-1px);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .btn-info {
            background: #0ea5e9;
            color: white;
        }

        .btn-info:hover {
            background: #0284c7;
            transform: translateY(-1px);
        }

        .table-container {
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }
        /* 可滚动表格容器 */
        .table-scroll-sm {
            max-height: 300px;
            overflow-y: auto;
            overflow-x: hidden;
        }
        .table-scroll-lg {
            max-height: 600px;
            overflow-y: auto;
            overflow-x: hidden;
        }
        /* 表头置顶以便滚动时可见 */
        .table thead th.sticky {
            position: sticky;
            top: 0;
            z-index: 2;
        }
        /* 自定义滚动条（WebKit） */
        .table-scroll-sm::-webkit-scrollbar,
        .table-scroll-lg::-webkit-scrollbar { width: 8px; }
        .table-scroll-sm::-webkit-scrollbar-thumb,
        .table-scroll-lg::-webkit-scrollbar-thumb { background: #fcd3b1; border-radius: 8px; }
        .table-scroll-sm:hover::-webkit-scrollbar-thumb,
        .table-scroll-lg:hover::-webkit-scrollbar-thumb { background: #f59e0b; }
        @media (max-width: 768px) {
            .table-scroll-lg { max-height: 420px; }
            .table-scroll-sm { max-height: 220px; }
        }

        .table {
            margin: 0;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            background: linear-gradient(135deg, #fefcfb 0%, #fef4ed 100%);
            border: none;
            padding: 1rem;
            font-weight: 600;
            color: #475569;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .table tbody td {
            padding: 1rem;
            border-top: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: #fefcfb;
        }

        .status-select {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        .status-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.1);
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 0 15px;
            }

            .page-header {
                padding: 1.5rem;
                margin-bottom: 1.5rem;
            }

            .page-header h1 {
                font-size: 1.5rem;
        }

            .stats-overview {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }

            .table-container {
                overflow-x: auto;
            }

            .d-mobile-none { display: none !important; }
            .status-select { width: 100%; font-size: 16px; padding: 12px 14px; }
            .btn, .btn-sm { padding: 12px 14px; }

            /* 移动端预约列表优化 */
            .mobile-appointment-list {
                max-height: 70vh;
                overflow-y: auto;
                overflow-x: hidden;
                padding-right: 4px;
                margin-right: -4px;
            }

            .mobile-appointment-card {
                background: white;
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                padding: 1rem;
                margin-bottom: 1rem;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }

            .mobile-appointment-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            .mobile-appointment-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 0.75rem;
            }

            .mobile-patient-name {
                font-size: 1.1rem;
                font-weight: 600;
                color: #1e293b;
                margin: 0;
            }

            .mobile-appointment-info {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 0.75rem;
                margin-bottom: 1rem;
            }

            .mobile-info-item {
                display: flex;
                flex-direction: column;
            }

            .mobile-info-label {
                font-size: 0.75rem;
                color: #64748b;
                font-weight: 500;
                margin-bottom: 0.25rem;
                text-transform: uppercase;
                letter-spacing: 0.025em;
            }

            .mobile-info-value {
                font-size: 0.9rem;
                color: #334155;
                font-weight: 500;
            }

            .mobile-phone-link {
                color: var(--primary-color);
                text-decoration: none;
                font-weight: 600;
            }

            .mobile-phone-link:hover {
                text-decoration: underline;
            }

            .mobile-status-container {
                margin-top: 1rem;
            }

            .mobile-status-label {
                font-size: 0.75rem;
                color: #64748b;
                font-weight: 500;
                margin-bottom: 0.5rem;
                text-transform: uppercase;
                letter-spacing: 0.025em;
            }

            /* 移动端滚动条样式 */
            .mobile-appointment-list::-webkit-scrollbar {
                width: 6px;
            }

            .mobile-appointment-list::-webkit-scrollbar-track {
                background: #f1f5f9;
                border-radius: 3px;
            }

            .mobile-appointment-list::-webkit-scrollbar-thumb {
                background: #cbd5e1;
                border-radius: 3px;
                transition: background 0.2s ease;
            }

            .mobile-appointment-list::-webkit-scrollbar-thumb:hover {
                background: #94a3b8;
            }

            /* Firefox 滚动条样式 */
            .mobile-appointment-list {
                scrollbar-width: thin;
                scrollbar-color: #cbd5e1 #f1f5f9;
            }

            /* 滚动指示器 */
            .mobile-scroll-indicator {
                position: sticky;
                top: 0;
                background: linear-gradient(to bottom, rgba(248, 250, 252, 0.9), transparent);
                height: 20px;
                margin-bottom: -20px;
                z-index: 1;
                pointer-events: none;
            }

            .mobile-scroll-indicator::after {
                content: '';
                position: absolute;
                top: 5px;
                left: 50%;
                transform: translateX(-50%);
                width: 30px;
                height: 3px;
                background: #cbd5e1;
                border-radius: 2px;
                opacity: 0.6;
            }
        }

        @media (min-width: 769px) {
            .mobile-appointment-list {
                display: none !important;
            }
        }

        @media (max-width: 768px) {
            .desktop-table-container {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1>
                <i class="bi bi-heart-pulse"></i>
                中医特色治疗管理系统
            </h1>
        </div>

        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-card-header">
                    <div class="stat-icon primary">
                        <i class="bi bi-calendar-check"></i>
                    </div>
                    <div>
                        <p class="stat-title">总预约数</p>
                        <p class="stat-value">{{ appointments|length }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-card-header">
                    <div class="stat-icon success">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <div>
                        <p class="stat-title">已就诊人次</p>
                        <p class="stat-value">{{ appointments|selectattr('status', 'equalto', '已就诊')|list|length }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-card-header">
                    <div class="stat-icon warning">
                        <i class="bi bi-clock-history"></i>
                    </div>
                    <div>
                        <p class="stat-title">待就诊人次</p>
                        <p class="stat-value">{{ appointments|selectattr('status', 'equalto', '未到')|list|length }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-card-header">
                    <div class="stat-icon success">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <div>
                        <p class="stat-title">已完成疗程</p>
                        <p class="stat-value">{{ stats|selectattr('count', 'ge', tcm_course_threshold)|list|length }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-card-header">
                    <div class="stat-icon warning">
                        <i class="bi bi-clock-history"></i>
                    </div>
                    <div>
                        <p class="stat-title">进行中疗程</p>
                        <p class="stat-value">{{ stats|selectattr('count', 'lt', tcm_course_threshold)|list|length }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-card-header">
                    <div class="stat-icon info">
                        <i class="bi bi-people"></i>
                    </div>
                    <div>
                        <p class="stat-title">患者总数</p>
                        <p class="stat-value">{{ stats|length }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 操作面板 -->
            <div class="content-card">
                <div class="card-header">
                    <h3><i class="bi bi-gear"></i> 系统管理</h3>
                </div>
                <div class="card-body">
                    <div class="action-buttons">
                        <a href="/tcm-admin/export" class="btn btn-success">
                            <i class="bi bi-download"></i>
                            导出Excel
                        </a>
                        <button class="btn btn-primary" id="btn-tcm-time-slot-manage">
                            <i class="bi bi-clock"></i>
                            时间段管理
                        </button>
                        <a href="/tcm-admin/daily-patients" class="btn btn-info">
                            <i class="bi bi-calendar-day"></i>
                            每日病人管理
                        </a>
                        <a href="/tcm-admin/search" class="btn btn-outline-primary">
                            <i class="bi bi-search"></i>
                            患者查询
                        </a>
                        <a href="/admin" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-left"></i>
                            盆底康复管理
                        </a>
                    </div>

                    <div class="threshold-setting">
                        <form class="d-flex align-items-center gap-3" id="tcm-course-threshold-form">
                            <label class="form-label mb-0 fw-semibold">中医特色治疗疗程完成阈值：</label>
                            <input type="number" class="form-control" style="width: 100px;"
                                   id="tcm-course-threshold-input" name="value" min="1" value="{{ tcm_course_threshold }}">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="bi bi-check"></i>
                                保存
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 疗程统计 -->
            <div class="content-card">
                <div class="card-header">
                    <h3><i class="bi bi-bar-chart"></i> 疗程统计</h3>
                </div>
                <div class="card-body">
                    <div class="table-container table-scroll-sm">
                        <table class="table">
                    <thead>
                        <tr>
                                    <th class="sticky">患者姓名</th>
                                    <th class="sticky">联系电话</th>
                                    <th class="sticky">就诊次数</th>
                                    <th class="sticky">疗程状态</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for stat in stats %}
                        <tr>
                                    <td class="fw-semibold">{{ stat.name }}</td>
                            <td>{{ stat.phone }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ stat.count }}</span>
                                    </td>
                            <td>
                                {% if stat.count >= tcm_course_threshold %}
                                                <span class="status-badge completed">疗程已完成</span>
                                {% else %}
                                                <span class="status-badge pending">进行中</span>
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
                    </div>
            </div>
        </div>

            <!-- 预约列表 -->
            <div class="content-card">
                <div class="card-header">
                    <h3><i class="bi bi-list-check"></i> 预约列表</h3>
                </div>
                <div class="card-body">
                    <!-- 桌面端表格视图 -->
                    <div class="desktop-table-container">
                        <div class="table-container table-scroll-lg">
                            <table class="table">
                    <thead>
                        <tr>
                                        <th class="sticky d-mobile-none">编号</th>
                                        <th class="sticky">患者</th>
                                        <th class="sticky">联系方式</th>
                                        <th class="sticky d-mobile-none">日期</th>
                                        <th class="sticky">时间段</th>
                                        <th class="sticky">状态</th>
                                        <th class="sticky d-mobile-none">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for appt in appointments %}
                        <tr>
                                        <td class="text-muted d-mobile-none">#{{ appt.id }}</td>
                                        <td class="fw-semibold">{{ appt.name }}</td>
                                        <td>
                                            <a href="tel:{{ appt.phone }}" class="text-decoration-none">{{ appt.phone }}</a>
                                        </td>
                                        <td class="d-mobile-none">{{ appt.date }}</td>
                            <td>{{ appt.time_slot }}</td>
                            <td>
                                            <form method="post" action="/tcm-admin/update-status" class="d-inline w-100">
                                    <input type="hidden" name="appointment_id" value="{{ appt.id }}">
                                    <select name="status" class="status-select" onchange="this.form.submit()">
                                        <option value="未到" {% if appt.status == '未到' %}selected{% endif %}>未到</option>
                                        <option value="已就诊" {% if appt.status == '已就诊' %}selected{% endif %}>已就诊</option>
                                        <option value="已取消" {% if appt.status == '已取消' %}selected{% endif %}>已取消</option>
                                    </select>
                                </form>
                            </td>
                                        <td class="d-mobile-none">
                                            <form method="post" action="/tcm-admin/delete" class="d-inline"
                                      onsubmit="return confirm('确定要删除这条预约记录吗？')">
                                    <input type="hidden" name="appointment_id" value="{{ appt.id }}">
                                                <button type="submit" class="btn btn-outline-danger btn-sm">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                </form>
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
                        </div>
                    </div>

                    <!-- 移动端卡片视图 -->
                    <div class="mobile-appointment-list d-block d-md-none">
                        <div class="mobile-scroll-indicator"></div>
                        {% for appt in appointments %}
                        <div class="mobile-appointment-card">
                            <div class="mobile-appointment-header">
                                <h4 class="mobile-patient-name">{{ appt.name }}</h4>
                                <span class="text-muted small">#{{ appt.id }}</span>
                            </div>

                            <div class="mobile-appointment-info">
                                <div class="mobile-info-item">
                                    <div class="mobile-info-label">联系方式</div>
                                    <div class="mobile-info-value">
                                        <a href="tel:{{ appt.phone }}" class="mobile-phone-link">
                                            <i class="bi bi-telephone"></i> {{ appt.phone }}
                                        </a>
                                    </div>
                                </div>

                                <div class="mobile-info-item">
                                    <div class="mobile-info-label">预约日期</div>
                                    <div class="mobile-info-value">
                                        <i class="bi bi-calendar"></i> {{ appt.date }}
                                    </div>
                                </div>

                                <div class="mobile-info-item">
                                    <div class="mobile-info-label">时间段</div>
                                    <div class="mobile-info-value">
                                        <i class="bi bi-clock"></i> {{ appt.time_slot }}
                                    </div>
                                </div>

                                <div class="mobile-info-item">
                                    <div class="mobile-info-label">当前状态</div>
                                    <div class="mobile-info-value">
                                        {% if appt.status == '已就诊' %}
                                            <span class="badge bg-success">{{ appt.status }}</span>
                                        {% elif appt.status == '未到' %}
                                            <span class="badge bg-warning">{{ appt.status }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ appt.status }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="mobile-status-container">
                                <div class="mobile-status-label">更新状态</div>
                                <form method="post" action="/tcm-admin/update-status" class="d-inline w-100">
                                    <input type="hidden" name="appointment_id" value="{{ appt.id }}">
                                    <select name="status" class="status-select" onchange="this.form.submit()">
                                        <option value="未到" {% if appt.status == '未到' %}selected{% endif %}>未到</option>
                                        <option value="已就诊" {% if appt.status == '已就诊' %}selected{% endif %}>已就诊</option>
                                        <option value="已取消" {% if appt.status == '已取消' %}selected{% endif %}>已取消</option>
                                    </select>
                                </form>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 中医时间段管理弹窗 -->
    <div class="modal fade" id="tcmTimeSlotModal" tabindex="-1" aria-labelledby="tcmTimeSlotModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
                    <h5 class="modal-title" id="tcmTimeSlotModalLabel">
                        <i class="bi bi-clock"></i> 中医特色治疗时间段管理
                    </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <table class="table table-bordered align-middle" id="tcm-time-slot-table">
              <thead>
                <tr>
                  <th>时间段</th>
                  <th>最大人数</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
            <hr>
            <form class="row g-3" id="add-tcm-time-slot-form">
              <div class="col-md-5">
                            <input type="text" class="form-control" id="new-tcm-time-slot-label" 
                                   placeholder="如 上午08:00-09:00" required>
              </div>
              <div class="col-md-3">
                            <input type="number" class="form-control" id="new-tcm-time-slot-max" 
                                   placeholder="最大人数" min="1" value="3" required>
              </div>
              <div class="col-md-2">
                            <button type="submit" class="btn btn-success w-100">
                                <i class="bi bi-plus"></i> 新增
                            </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 中医时间段管理弹窗逻辑
    const tcmTimeSlotModal = new bootstrap.Modal(document.getElementById('tcmTimeSlotModal'));
    document.getElementById('btn-tcm-time-slot-manage').onclick = function() {
      loadTcmTimeSlots();
      tcmTimeSlotModal.show();
    };

    function loadTcmTimeSlots() {
      fetch('/tcm-admin/time-slots').then(r=>r.json()).then(res => {
        if(res.success) {
          const tbody = document.querySelector('#tcm-time-slot-table tbody');
          tbody.innerHTML = '';
          res.data.forEach(slot => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
              <td><input type="text" class="form-control form-control-sm" value="${slot.label}" onchange="updateTcmSlotLabel(${slot.id}, this.value)"></td>
              <td><input type="number" class="form-control form-control-sm" value="${slot.max_count}" min="1" onchange="updateTcmSlotMax(${slot.id}, this.value)"></td>
              <td>
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" ${slot.enabled ? 'checked' : ''} onchange="toggleTcmSlotEnabled(${slot.id}, this.checked)">
                </div>
              </td>
                        <td><button class="btn btn-danger btn-sm" onclick="deleteTcmSlot(${slot.id})"><i class="bi bi-trash"></i></button></td>
            `;
            tbody.appendChild(tr);
          });
        }
      });
    }

    function updateTcmSlotLabel(id, value) {
      fetch(`/tcm-admin/time-slots/${id}`, {
        method: 'PUT',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({label: value})
      }).then(()=>loadTcmTimeSlots());
    }

    function updateTcmSlotMax(id, value) {
      fetch(`/tcm-admin/time-slots/${id}`, {
        method: 'PUT',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({max_count: value})
      }).then(()=>loadTcmTimeSlots());
    }

    function toggleTcmSlotEnabled(id, enabled) {
      fetch(`/tcm-admin/time-slots/${id}/enabled`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({enabled})
      }).then(()=>loadTcmTimeSlots());
    }

    function deleteTcmSlot(id) {
      if(confirm('确定要删除该时间段吗？')) {
        fetch(`/tcm-admin/time-slots/${id}`, {method: 'DELETE'}).then(()=>loadTcmTimeSlots());
      }
    }

    document.getElementById('add-tcm-time-slot-form').onsubmit = function(e) {
      e.preventDefault();
      const label = document.getElementById('new-tcm-time-slot-label').value.trim();
      const max_count = document.getElementById('new-tcm-time-slot-max').value;
      if(label && max_count) {
        fetch('/tcm-admin/time-slots', {
          method: 'POST',
          headers: {'Content-Type': 'application/json'},
          body: JSON.stringify({label, max_count})
        }).then(()=>{
          loadTcmTimeSlots();
          document.getElementById('new-tcm-time-slot-label').value = '';
          document.getElementById('new-tcm-time-slot-max').value = '3';
        });
      }
    };

    // 中医特色治疗疗程完成阈值设置
    document.getElementById('tcm-course-threshold-form').onsubmit = function(e) {
      e.preventDefault();
      const value = document.getElementById('tcm-course-threshold-input').value;
      const formData = new FormData();
      formData.append('value', value);

      fetch('/tcm-admin/tcm-course-threshold', {
        method: 'POST',
        body: formData
      }).then(response => {
        if(response.ok) {
          alert('中医特色治疗疗程完成阈值设置成功！');
          location.reload();
        } else {
          alert('设置失败，请重试');
        }
      });
    };
    </script>
</body>
</html>
