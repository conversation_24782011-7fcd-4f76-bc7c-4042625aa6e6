import multiprocessing

# 绑定的IP和端口 - 更新为5010端口
bind = "0.0.0.0:5010"
backlog = 2048

# 工作进程数 - 性能优化
workers = min(multiprocessing.cpu_count() * 2 + 1, 8)

# 工作模式 - 使用默认的sync模式（更稳定）
worker_class = 'sync'

# 超时时间 - 增加超时时间
timeout = 60

# 优雅重启时间
graceful_timeout = 30

# 保持连接时间 - 增加keepalive时间
keepalive = 5

# 性能优化配置
max_requests = 2000
max_requests_jitter = 100
preload_app = True
worker_tmp_dir = "/dev/shm"
worker_rlimit_nofile = 65535

# 日志配置 - 增加性能监控
accesslog = "/var/log/gunicorn/access.log"
errorlog = "/var/log/gunicorn/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'
capture_output = True
enable_stdio_inheritance = True

# 进程命名
proc_name = "hospital_appointment_system"

# 用户权限
user = "www-data"
group = "www-data"

# 性能调优
sendfile = True
tcp_nodelay = True
reuse_port = True

# 后台运行
daemon = False

# pid文件
pidfile = "/var/run/gunicorn.pid"

# 监控钩子
def when_ready(server):
    server.log.info("医院预约系统服务器准备就绪")

def worker_int(worker):
    worker.log.info("工作进程中断")

def pre_fork(server, worker):
    server.log.info("工作进程分叉前")

def post_fork(server, worker):
    server.log.info("工作进程分叉后")

