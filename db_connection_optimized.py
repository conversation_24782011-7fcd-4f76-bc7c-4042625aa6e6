import pymysql
import logging
from db_pool import get_db_pool
from cache_manager import get_cache_manager, cached
from typing import List, Dict, Any, Optional, Tuple

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OptimizedDatabase:
    """优化的数据库操作类，使用连接池和缓存"""
    
    def __init__(self):
        self.pool = get_db_pool()
        self.cache = get_cache_manager()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 连接池管理连接，这里不需要手动关闭
        pass
    
    def _execute_query(self, query: str, params: tuple = None, fetch_one: bool = False, fetch_all: bool = False):
        """执行数据库查询的通用方法"""
        with self.pool.get_connection() as connection:
            cursor = connection.cursor()
            try:
                cursor.execute(query, params or ())
                
                if fetch_one:
                    return cursor.fetchone()
                elif fetch_all:
                    return cursor.fetchall()
                else:
                    connection.commit()
                    return cursor.rowcount
            except Exception as e:
                connection.rollback()
                logger.error(f"数据库查询错误: {e}, SQL: {query}")
                raise
            finally:
                cursor.close()
    
    def add_rehab_appointment(self, name: str, phone: str, appointment_date: str, time_slot: str) -> Tuple[bool, str, int]:
        """添加盆底康复预约"""
        try:
            # 清除相关缓存
            self.cache.clear_pattern(f"rehab:*:{phone}")
            self.cache.clear_pattern("rehab:appointments:*")
            
            # 获取当天的最大编号
            get_max_number_query = """
                SELECT COALESCE(MAX(daily_number), 0)
                FROM pelvic_rehab
                WHERE DATE(created_at) = CURRENT_DATE
            """
            max_number = self._execute_query(get_max_number_query, fetch_one=True)[0]
            new_number = max_number + 1

            # 检查是否存在当天创建的预约
            check_query = """
                SELECT id, daily_number FROM pelvic_rehab 
                WHERE phone = %s 
                AND DATE(created_at) = CURRENT_DATE
            """
            existing = self._execute_query(check_query, (phone,), fetch_one=True)
            
            if existing:
                # 更新现有预约
                update_query = """
                    UPDATE pelvic_rehab 
                    SET name = %s, appointment_date = %s, time_slot = %s
                    WHERE id = %s
                """
                self._execute_query(update_query, (name, appointment_date, time_slot, existing[0]))
                logger.debug(f"更新今日预约: {phone}, 预约编号: {existing[1]}")
                return True, "更新", existing[1]
            else:
                # 创建新预约
                insert_query = """
                    INSERT INTO pelvic_rehab 
                    (daily_number, name, phone, appointment_date, time_slot)
                    VALUES (%s, %s, %s, %s, %s)
                """
                self._execute_query(insert_query, (new_number, name, phone, appointment_date, time_slot))
                logger.debug(f"新预约添加成功: {phone}, 预约编号: {new_number}")
                return True, "新增", new_number
                
        except Exception as err:
            logger.error(f"添加盆底康复预约错误: {err}")
            return False, None, None

    def add_tcm_appointment(self, name: str, phone: str, appointment_date: str, time_slot: str) -> Tuple[bool, str, int]:
        """添加中医特色治疗预约"""
        try:
            # 清除相关缓存
            self.cache.clear_pattern(f"tcm:*:{phone}")
            self.cache.clear_pattern("tcm:appointments:*")
            
            # 获取当天的最大编号
            get_max_number_query = """
                SELECT COALESCE(MAX(daily_number), 0)
                FROM tcm_treatment
                WHERE DATE(created_at) = CURRENT_DATE
            """
            max_number = self._execute_query(get_max_number_query, fetch_one=True)[0]
            new_number = max_number + 1

            # 检查是否存在当天创建的预约
            check_query = """
                SELECT id, daily_number FROM tcm_treatment
                WHERE phone = %s
                AND DATE(created_at) = CURRENT_DATE
            """
            existing = self._execute_query(check_query, (phone,), fetch_one=True)

            if existing:
                # 更新现有预约
                update_query = """
                    UPDATE tcm_treatment
                    SET name = %s, appointment_date = %s, time_slot = %s
                    WHERE id = %s
                """
                self._execute_query(update_query, (name, appointment_date, time_slot, existing[0]))
                logger.debug(f"更新今日中医预约: {phone}, 预约编号: {existing[1]}")
                return True, "更新", existing[1]
            else:
                # 创建新预约
                insert_query = """
                    INSERT INTO tcm_treatment
                    (daily_number, name, phone, appointment_date, time_slot)
                    VALUES (%s, %s, %s, %s, %s)
                """
                self._execute_query(insert_query, (new_number, name, phone, appointment_date, time_slot))
                logger.debug(f"新中医预约添加成功: {phone}, 预约编号: {new_number}")
                return True, "新增", new_number

        except Exception as err:
            logger.error(f"添加中医预约错误: {err}")
            return False, None, None

    @cached(timeout=60, key_prefix="appointment_count")
    def get_appointment_count(self, appointment_type: str, appointment_date: str, time_slot: str) -> int:
        """获取预约数量（带缓存）"""
        try:
            if appointment_type == 'pelvic_rehab':
                table = 'pelvic_rehab'
            elif appointment_type == 'tcm_treatment':
                table = 'tcm_treatment'
            else:
                return 0
            
            query = f"""
                SELECT COUNT(*)
                FROM {table}
                WHERE appointment_date = %s AND time_slot = %s
            """
            result = self._execute_query(query, (appointment_date, time_slot), fetch_one=True)
            return result[0] if result else 0
        except Exception as err:
            logger.error(f"查询预约数量错误: {err}")
            return 0

    @cached(timeout=300, key_prefix="rehab_appointments")
    def get_rehab_appointments(self, phone: str) -> List[Dict[str, Any]]:
        """获取盆底康复预约记录（带缓存）"""
        try:
            query = """
                SELECT name, phone, appointment_date, time_slot, daily_number, created_at
                FROM pelvic_rehab
                WHERE phone = %s
                ORDER BY created_at DESC, appointment_date ASC
                LIMIT 5
            """
            results = self._execute_query(query, (phone,), fetch_all=True)
            
            appointments = []
            for row in results:
                appointments.append({
                    'name': row[0],
                    'phone': row[1],
                    'appointment_date': row[2].strftime('%Y年%m月%d日'),
                    'time_slot': row[3],
                    'daily_number': row[4],
                    'created_at': row[5].strftime('%Y年%m月%d日')
                })
            return appointments
        except Exception as err:
            logger.error(f"查询盆底康复预约记录错误: {err}")
            return []

    @cached(timeout=300, key_prefix="tcm_appointments")
    def get_tcm_appointments(self, phone: str) -> List[Dict[str, Any]]:
        """获取中医特色治疗预约记录（带缓存）"""
        try:
            query = """
                SELECT name, phone, appointment_date, time_slot, daily_number, created_at, status
                FROM tcm_treatment
                WHERE phone = %s
                ORDER BY created_at DESC, appointment_date ASC
                LIMIT 5
            """
            results = self._execute_query(query, (phone,), fetch_all=True)

            appointments = []
            for row in results:
                appointments.append({
                    'name': row[0],
                    'phone': row[1],
                    'appointment_date': row[2].strftime('%Y年%m月%d日'),
                    'time_slot': row[3],
                    'daily_number': row[4],
                    'created_at': row[5].strftime('%Y年%m月%d日'),
                    'status': row[6]
                })
            return appointments
        except Exception as err:
            logger.error(f"查询中医预约记录错误: {err}")
            return []

    def update_appointment_status(self, appointment_id: int, new_status: str):
        """更新盆底康复预约状态"""
        try:
            # 清除相关缓存
            self.cache.clear_pattern("rehab:*")
            
            query = "UPDATE pelvic_rehab SET status = %s WHERE id = %s"
            self._execute_query(query, (new_status, appointment_id))
        except Exception as e:
            logger.error(f'更新预约状态失败: {e}')

    def update_tcm_appointment_status(self, appointment_id: int, new_status: str):
        """更新中医预约状态"""
        try:
            # 清除相关缓存
            self.cache.clear_pattern("tcm:*")
            
            query = "UPDATE tcm_treatment SET status = %s WHERE id = %s"
            self._execute_query(query, (new_status, appointment_id))
        except Exception as e:
            logger.error(f'更新中医预约状态失败: {e}')

    # 设置相关方法
    def get_setting(self, key: str, default=None):
        """获取设置"""
        cache_key = f"setting:{key}"
        cached_value = self.cache.get(cache_key)
        if cached_value is not None:
            return cached_value
        
        try:
            self._execute_query("""
                CREATE TABLE IF NOT EXISTS settings (
                    `key` VARCHAR(64) PRIMARY KEY,
                    `value` VARCHAR(255) NOT NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局设置表';
            """)
            
            result = self._execute_query("SELECT value FROM settings WHERE `key`=%s", (key,), fetch_one=True)
            value = int(result[0]) if result else default
            
            # 缓存结果
            self.cache.set(cache_key, value, 3600)  # 缓存1小时
            return value
        except Exception as e:
            logger.error(f'获取设置失败: {e}')
            return default

    def set_setting(self, key: str, value):
        """设置参数"""
        try:
            # 清除缓存
            self.cache.delete(f"setting:{key}")

            self._execute_query("""
                CREATE TABLE IF NOT EXISTS settings (
                    `key` VARCHAR(64) PRIMARY KEY,
                    `value` VARCHAR(255) NOT NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局设置表';
            """)
            self._execute_query("REPLACE INTO settings (`key`, `value`) VALUES (%s, %s)", (key, str(value)))
        except Exception as e:
            logger.error(f'设置参数失败: {e}')

    @cached(timeout=300, key_prefix="all_rehab_appointments")
    def get_all_rehab_appointments(self) -> List[Dict[str, Any]]:
        """获取所有盆底康复预约"""
        try:
            query = """
                SELECT id, name, phone, appointment_date, time_slot, \
                       IFNULL(status, '未到') as status, daily_number, created_at
                FROM pelvic_rehab
                ORDER BY appointment_date DESC, time_slot ASC, id DESC
            """
            results = self._execute_query(query, fetch_all=True)
            appointments = []
            for row in results:
                appointments.append({
                    'id': row[0],
                    'name': row[1],
                    'phone': row[2],
                    'date': row[3].strftime('%Y-%m-%d'),
                    'time_slot': row[4],
                    'status': row[5],
                    'daily_number': row[6],
                    'created_at': row[7].strftime('%Y-%m-%d')
                })
            return appointments
        except Exception as e:
            logger.error(f'获取所有盆底康复预约失败: {e}')
            return []

    @cached(timeout=300, key_prefix="all_tcm_appointments")
    def get_all_tcm_appointments(self) -> List[Dict[str, Any]]:
        """获取所有中医特色治疗预约"""
        try:
            query = """
                SELECT id, name, phone, appointment_date, time_slot,
                       IFNULL(status, '未到') as status, daily_number, created_at
                FROM tcm_treatment
                ORDER BY appointment_date DESC, time_slot ASC, id DESC
            """
            results = self._execute_query(query, fetch_all=True)
            appointments = []
            for row in results:
                appointments.append({
                    'id': row[0],
                    'name': row[1],
                    'phone': row[2],
                    'date': row[3].strftime('%Y-%m-%d'),
                    'time_slot': row[4],
                    'status': row[5],
                    'daily_number': row[6],
                    'created_at': row[7].strftime('%Y-%m-%d')
                })
            return appointments
        except Exception as e:
            logger.error(f'获取所有中医特色治疗预约失败: {e}')
            return []

    @cached(timeout=600, key_prefix="rehab_statistics")
    def get_rehab_statistics(self) -> List[Dict[str, Any]]:
        """获取盆底康复统计信息"""
        try:
            query = """
                SELECT name, phone, COUNT(*) as count
                FROM pelvic_rehab
                WHERE status = '已就诊'
                GROUP BY name, phone
                ORDER BY count DESC
            """
            results = self._execute_query(query, fetch_all=True)
            stats = []
            for row in results:
                stats.append({
                    'name': row[0],
                    'phone': row[1],
                    'count': row[2]
                })
            return stats
        except Exception as e:
            logger.error(f'统计盆底康复预约次数失败: {e}')
            return []

    @cached(timeout=600, key_prefix="tcm_statistics")
    def get_tcm_statistics(self) -> List[Dict[str, Any]]:
        """获取中医特色治疗统计信息"""
        try:
            query = """
                SELECT name, phone, COUNT(*) as count
                FROM tcm_treatment
                WHERE status = '已就诊'
                GROUP BY name, phone
                ORDER BY count DESC
            """
            results = self._execute_query(query, fetch_all=True)
            stats = []
            for row in results:
                stats.append({
                    'name': row[0],
                    'phone': row[1],
                    'count': row[2]
                })
            return stats
        except Exception as e:
            logger.error(f'统计中医预约次数失败: {e}')
            return []

    @cached(timeout=300, key_prefix="pelvic_time_slots")
    def get_pelvic_time_slots(self) -> List[Dict[str, Any]]:
        """获取盆底康复时间段"""
        try:
            query = "SELECT id, label, enabled, max_count FROM pelvic_time_slots ORDER BY id ASC"
            results = self._execute_query(query, fetch_all=True)

            time_slots = []
            for row in results:
                time_slots.append({
                    'id': row[0],
                    'label': row[1],
                    'enabled': bool(row[2]),
                    'max_count': row[3]
                })
            return time_slots
        except Exception as e:
            logger.error(f'获取盆底康复时间段失败: {e}')
            return []

    @cached(timeout=300, key_prefix="tcm_time_slots")
    def get_tcm_time_slots(self) -> List[Dict[str, Any]]:
        """获取中医特色治疗时间段"""
        try:
            query = "SELECT id, label, enabled, max_count FROM tcm_time_slots ORDER BY id ASC"
            results = self._execute_query(query, fetch_all=True)

            time_slots = []
            for row in results:
                time_slots.append({
                    'id': row[0],
                    'label': row[1],
                    'enabled': bool(row[2]),
                    'max_count': row[3]
                })
            return time_slots
        except Exception as e:
            logger.error(f'获取中医特色治疗时间段失败: {e}')
            return []

    def delete_appointment(self, appointment_id: int):
        """删除盆底康复预约"""
        try:
            # 清除相关缓存
            self.cache.clear_pattern("rehab:*")

            query = "DELETE FROM pelvic_rehab WHERE id = %s"
            self._execute_query(query, (appointment_id,))
        except Exception as e:
            logger.error(f'删除预约失败: {e}')

    def delete_tcm_appointment(self, appointment_id: int):
        """删除中医预约"""
        try:
            # 清除相关缓存
            self.cache.clear_pattern("tcm:*")

            query = "DELETE FROM tcm_treatment WHERE id = %s"
            self._execute_query(query, (appointment_id,))
        except Exception as e:
            logger.error(f'删除中医预约失败: {e}')
