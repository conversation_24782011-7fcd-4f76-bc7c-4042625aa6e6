#!/bin/bash

# 优化版本部署脚本
# 安全地将优化后的代码部署到服务器 pd.beimoyinhenlinlin.cn

set -e  # 遇到错误立即退出

SERVER="myserver"
REMOTE_PATH="/var/www/hospital-appointment"
DOMAIN="pd.beimoyinhenlinlin.cn"

echo "开始部署优化版本到服务器 $DOMAIN..."

# 1. 创建备份
echo "1. 创建当前版本备份..."
ssh $SERVER "cd $REMOTE_PATH && \
    sudo mkdir -p backups && \
    sudo tar -czf backups/backup_$(date +%Y%m%d_%H%M%S).tar.gz \
    --exclude=backups --exclude=venv --exclude=__pycache__ --exclude=.git ."

# 2. 停止服务
echo "2. 停止当前服务..."
ssh $SERVER "sudo systemctl stop hospital-appointment || true"

# 3. 上传新文件
echo "3. 上传优化后的文件..."

# 上传核心优化文件
scp requirements.txt $SERVER:$REMOTE_PATH/
scp db_pool.py $SERVER:$REMOTE_PATH/
scp cache_manager.py $SERVER:$REMOTE_PATH/
scp db_connection_optimized.py $SERVER:$REMOTE_PATH/
scp static_optimizer.py $SERVER:$REMOTE_PATH/
scp app_optimized.py $SERVER:$REMOTE_PATH/
scp migrate_to_optimized.py $SERVER:$REMOTE_PATH/
scp test_optimization.py $SERVER:$REMOTE_PATH/
scp OPTIMIZATION_GUIDE.md $SERVER:$REMOTE_PATH/

# 上传更新的服务文件
scp -r services/ $SERVER:$REMOTE_PATH/
scp -r routes/ $SERVER:$REMOTE_PATH/
scp -r templates/ $SERVER:$REMOTE_PATH/

# 4. 设置权限
echo "4. 设置文件权限..."
ssh $SERVER "cd $REMOTE_PATH && \
    sudo chown -R www-data:www-data . && \
    sudo chmod +x *.py && \
    sudo chmod 644 requirements.txt"

# 5. 安装依赖
echo "5. 安装新的依赖包..."
ssh $SERVER "cd $REMOTE_PATH && \
    source venv/bin/activate && \
    pip install -r requirements.txt"

# 6. 运行数据迁移
echo "6. 运行数据迁移..."
ssh $SERVER "cd $REMOTE_PATH && \
    source venv/bin/activate && \
    python migrate_to_optimized.py"

# 7. 运行测试
echo "7. 运行功能测试..."
ssh $SERVER "cd $REMOTE_PATH && \
    source venv/bin/activate && \
    python test_optimization.py"

# 8. 更新systemd服务文件
echo "8. 更新systemd服务配置..."
ssh $SERVER "cd $REMOTE_PATH && \
    sudo cp hospital-appointment.service /etc/systemd/system/ && \
    sudo systemctl daemon-reload"

# 9. 启动优化版本服务
echo "9. 启动优化版本服务..."
ssh $SERVER "sudo systemctl start hospital-appointment && \
    sudo systemctl enable hospital-appointment"

# 10. 检查服务状态
echo "10. 检查服务状态..."
sleep 5
ssh $SERVER "systemctl status hospital-appointment --no-pager"

# 11. 测试应用响应
echo "11. 测试应用响应..."
ssh $SERVER "curl -s http://localhost:5010/status | head -c 200"

echo ""
echo "✅ 优化版本部署完成！"
echo "📊 访问 http://your-domain/status 查看系统状态"
echo "🔧 访问 http://your-domain/admin 管理盆底康复"
echo "🏥 访问 http://your-domain/tcm-admin 管理中医特色治疗"
echo ""
echo "如果遇到问题，可以使用以下命令回滚："
echo "ssh $SERVER 'cd $REMOTE_PATH && sudo tar -xzf backups/backup_*.tar.gz'"
