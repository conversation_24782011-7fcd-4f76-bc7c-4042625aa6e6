"""
性能监控工具
"""
import time
import functools
import logging
from typing import Dict, Any
import threading
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    def __init__(self, max_records: int = 1000):
        self.max_records = max_records
        self.request_times = deque(maxlen=max_records)
        self.db_query_times = deque(maxlen=max_records)
        self.cache_stats = defaultdict(int)
        self.lock = threading.RLock()
    
    def record_request_time(self, endpoint: str, duration: float):
        """记录请求处理时间"""
        with self.lock:
            self.request_times.append({
                'endpoint': endpoint,
                'duration': duration,
                'timestamp': time.time()
            })
    
    def record_db_query_time(self, query_type: str, duration: float):
        """记录数据库查询时间"""
        with self.lock:
            self.db_query_times.append({
                'query_type': query_type,
                'duration': duration,
                'timestamp': time.time()
            })
    
    def record_cache_hit(self):
        """记录缓存命中"""
        with self.lock:
            self.cache_stats['hits'] += 1
    
    def record_cache_miss(self):
        """记录缓存未命中"""
        with self.lock:
            self.cache_stats['misses'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        with self.lock:
            total_requests = len(self.request_times)
            total_queries = len(self.db_query_times)
            
            avg_request_time = 0
            avg_query_time = 0
            
            if total_requests > 0:
                avg_request_time = sum(r['duration'] for r in self.request_times) / total_requests
            
            if total_queries > 0:
                avg_query_time = sum(q['duration'] for q in self.db_query_times) / total_queries
            
            cache_hit_rate = 0
            total_cache_requests = self.cache_stats['hits'] + self.cache_stats['misses']
            if total_cache_requests > 0:
                cache_hit_rate = self.cache_stats['hits'] / total_cache_requests * 100
            
            return {
                'total_requests': total_requests,
                'avg_request_time': round(avg_request_time, 3),
                'total_db_queries': total_queries,
                'avg_query_time': round(avg_query_time, 3),
                'cache_hit_rate': round(cache_hit_rate, 2),
                'cache_hits': self.cache_stats['hits'],
                'cache_misses': self.cache_stats['misses']
            }

# 全局性能监控实例
monitor = PerformanceMonitor()

def timing_decorator(operation_type: str = "request"):
    """性能计时装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                if operation_type == "request":
                    monitor.record_request_time(func.__name__, duration)
                elif operation_type == "db_query":
                    monitor.record_db_query_time(func.__name__, duration)
        return wrapper
    return decorator

def log_slow_queries(threshold: float = 1.0):
    """记录慢查询装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            
            if duration > threshold:
                logger.warning(f"慢查询检测: {func.__name__} 耗时 {duration:.3f}s")
            
            return result
        return wrapper
    return decorator
